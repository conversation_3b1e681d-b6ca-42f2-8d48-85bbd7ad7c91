package org.example.service;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.model.ShippingCalculationRequest;
import org.example.model.ShippingCalculationResult;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.math.BigDecimal;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@SpringBootTest
public class ShippingCalculationServiceTest {

    private ShippingCalculationService calculationService;
    private ObjectMapper objectMapper;

    @BeforeEach
    void setUp() {
        objectMapper = new ObjectMapper();
        calculationService = new ShippingCalculationService(objectMapper);
    }

    @Test
    void testCalculateShipping() throws Exception {
        // 读取测试JSON文件
        String jsonContent = Files.readString(Paths.get("test-calculation.json"));
        
        // 创建测试请求
        ShippingCalculationRequest request = new ShippingCalculationRequest();
        request.setOriginPort("PEK");
        request.setDestinationPort("AMS"); // 转运点
        request.setWeight(new BigDecimal("150")); // 在100-300范围内
        request.setVolume(new BigDecimal("2")); // 密度 = 150/2 = 75，小于80
        request.setJsonContents(Arrays.asList(jsonContent));

        // 执行计算
        List<ShippingCalculationResult> results = calculationService.calculateShipping(request);

        // 验证结果
        assertNotNull(results);
        assertEquals(1, results.size());
        
        ShippingCalculationResult result = results.get(0);
        assertFalse(result.isHasError(), "计算应该成功，错误信息: " + result.getErrorMessage());
        
        // 验证密度计算
        assertEquals(new BigDecimal("75.00"), result.getCalculatedDensity());
        
        // 验证密度因子匹配
        assertEquals(Integer.valueOf(80), result.getMatchedDensityFactor());
        
        // 验证基础费率
        assertEquals(new BigDecimal("26"), result.getBaseRate());
        
        // 验证是否识别为转运点
        assertTrue(result.isTransferPoint());
        
        // 验证转运费率
        assertEquals(new BigDecimal("1"), result.getTransferRate());
        
        // 验证总费用计算
        BigDecimal expectedBaseCost = new BigDecimal("26").multiply(new BigDecimal("150")); // 26 * 150 = 3900
        BigDecimal expectedTransferCost = new BigDecimal("1").multiply(new BigDecimal("150")); // 1 * 150 = 150
        BigDecimal expectedTotalCost = expectedBaseCost.add(expectedTransferCost); // 3900 + 150 = 4050
        
        assertEquals(expectedBaseCost, result.getBaseCost());
        assertEquals(expectedTransferCost, result.getTransferCost());
        assertEquals(expectedTotalCost, result.getTotalCost());
        
        System.out.println("测试成功！");
        System.out.println("计算详情: " + result.getCalculationDetails());
    }

    @Test
    void testDirectDestination() throws Exception {
        // 读取测试JSON文件
        String jsonContent = Files.readString(Paths.get("test-calculation.json"));
        
        // 创建测试请求 - 直达目的地
        ShippingCalculationRequest request = new ShippingCalculationRequest();
        request.setOriginPort("PEK");
        request.setDestinationPort("FRA"); // 直达目的地
        request.setWeight(new BigDecimal("200"));
        request.setVolume(new BigDecimal("2")); // 密度 = 200/2 = 100，大于80
        request.setJsonContents(Arrays.asList(jsonContent));

        // 执行计算
        List<ShippingCalculationResult> results = calculationService.calculateShipping(request);

        // 验证结果
        assertNotNull(results);
        assertEquals(1, results.size());
        
        ShippingCalculationResult result = results.get(0);
        assertFalse(result.isHasError(), "计算应该成功，错误信息: " + result.getErrorMessage());
        
        // 验证不是转运点
        assertFalse(result.isTransferPoint());
        
        // 验证转运费用为0
        assertEquals(BigDecimal.ZERO, result.getTransferCost());
        
        System.out.println("直达目的地测试成功！");
        System.out.println("计算详情: " + result.getCalculationDetails());
    }
}
