项目需求：开发一个运价测算工具页面

​目标：​​ 创建一个美观的网页界面，输入物流信息并基于导入的报价JSON文件计算运费。

​核心功能：​​

​用户输入：​​
始发港 (Origin Port)
目的港 (Destination Port)
货物重量 (Weight) - 单位：千克 (kg)
货物体积 (Volume) - 单位：立方米 (cbm)
​数据加载：​​
json文件从数据库表中读取，基于现有项目，pdf对应一个json，都在表中
如果有多个，就计算多份报价，把能够匹配出来的价格展示出来
​核心计算逻辑：​​
​计算密度：​​ 密度 = 重量(kg) / 体积(cbm)
​匹配密度因子：​​ 在每个报价JSON中，找到密度因子刚好大于等于计算出的密度的最小的那个密度因子。
例： 当前JSON中有密度因子: [100, 80]，计算密度为70。
匹配过程： 密度70 <= 80 (成立), 70 <= 100 (成立). 选择密度因子80（因为80 < 100，且80 >= 70）。
​确定基础运费区间：​​ 在上一步匹配到的密度因子对应的价格表中，再根据输入的重量(kg)找到对应的报价区间 (重量需满足区间下限 <= input重量 < 区间上限)。
​基础运费计算：​​ 基础运费 = 匹配到的报价(元/kg) * 输入重量(kg)
​处理转运点：​​
如果输入的目的港存在于当前报价JSON的转运点列表中：
先计算从始发港到目的港对应的主目的地（通常是某个大区或核心港）的基础运费（步骤如上）。
再加上从该主目的地到具体输入目的港的转运报价（需要在本JSON中找到该目的港对应的特定转运报价，单位通常是元/kg）乘以输入重量(kg)：总运费 = 到主目的地基础运费 + (转运报价 * 重量)
如果目的港不是转运点：总运费 = 基础运费
​多报价处理：​​ 对每个导入的JSON文件，​独立执行以上所有计算步骤。
​输出展示：​​
​清晰列出：​​ 对于每一个计算过的JSON报价文件，输出以下信息：
JSON文件名/标识符
匹配到的密度因子
适用的基础报价 (元/kg)
判断过程（是否为转运点？使用的基础港和转运港分别是什么？）
最终计算出的总运费
计费依据的详细规则摘要（如：重量范围、密度因子、基础港、转运点、具体报价值）
​展示方式：​​ 结果在页面上以美观、结构化的方式呈现（例如表格、卡片等）。
​界面要求：​​
直观的输入表单（4个输入字段）。
文件上传区域，支持选择和导入多个JSON文件。
显著的计算按钮。
美观的结果展示区域，清晰分隔显示每个JSON文件的详细计算结果。
​关键逻辑重申：​​

​密度因子匹配原则：​​ 找到 大于等于计算密度 的 最小 密度因子。这是匹配的核心规则。
​转运点判断：​​ 目的港是否在转运点列表中决定了是否触发额外转运费计算。
​多JSON独立计算：​​ 系统需处理并展示来自所有导入JSON的不同计算结果。
​请帮我生成代码​​

代码需要健壮，能处理可能的输入错误（如非数字、文件格式错误、JSON结构不符等）。
界面设计尽量简洁美观。