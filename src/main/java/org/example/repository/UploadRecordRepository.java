package org.example.repository;

import org.example.model.UploadRecord;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface UploadRecordRepository extends JpaRepository<UploadRecord, Long> {
    
    @Query("SELECT u FROM UploadRecord u ORDER BY u.uploadTime DESC")
    List<UploadRecord> findAllByOrderByUploadTimeDesc();
}
