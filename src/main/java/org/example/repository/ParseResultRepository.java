package org.example.repository;

import org.example.model.ParseResult;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ParseResultRepository extends JpaRepository<ParseResult, Long> {
    Optional<ParseResult> findByRecordId(Long recordId);

    /**
     * 查询所有关联到未删除上传记录的解析结果
     * 只返回那些能关联到upload_records表中存在记录的解析结果
     */
    @Query("SELECT pr FROM ParseResult pr WHERE pr.recordId IN " +
           "(SELECT ur.id FROM UploadRecord ur)")
    List<ParseResult> findAllWithValidUploadRecord();
}
