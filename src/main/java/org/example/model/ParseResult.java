package org.example.model;

import javax.persistence.*;
import java.util.Date;

@Entity
@Table(name = "parse_results")
public class ParseResult {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "record_id", nullable = false)
    private Long recordId;
    
    @Column(name = "json_result", columnDefinition = "TEXT")
    private String jsonResult;
    
    @Column(name = "parse_time")
    private Date parseTime;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public Long getRecordId() {
        return recordId;
    }

    public void setRecordId(Long recordId) {
        this.recordId = recordId;
    }

    public String getJsonResult() {
        return jsonResult;
    }

    public void setJsonResult(String jsonResult) {
        this.jsonResult = jsonResult;
    }

    public Date getParseTime() {
        return parseTime;
    }

    public void setParseTime(Date parseTime) {
        this.parseTime = parseTime;
    }
}
