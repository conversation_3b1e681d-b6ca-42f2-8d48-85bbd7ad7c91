package org.example.model;

import com.fasterxml.jackson.annotation.JsonProperty;
import java.math.BigDecimal;
import java.util.List;

/**
 * 运价测算结果实体
 */
public class ShippingCalculationResult {
    
    private String jsonFileName;           // JSON文件名/标识符
    private BigDecimal calculatedDensity;  // 计算出的密度
    private Integer matchedDensityFactor;  // 匹配到的密度因子
    private BigDecimal baseRate;           // 适用的基础报价(元/kg)
    @JsonProperty("isTransferPoint")
    private boolean isTransferPoint;       // 是否为转运点
    private String basePort;               // 基础港
    private String transferPort;           // 转运港
    private BigDecimal transferRate;       // 转运报价(元/kg)
    private BigDecimal baseCost;           // 基础运费
    private BigDecimal transferCost;       // 转运费用
    private BigDecimal totalCost;          // 最终总运费
    private String calculationDetails;     // 计费依据的详细规则摘要
    private boolean hasError;              // 是否有错误
    private String errorMessage;           // 错误信息
    private RouteInfo routeInfo;           // 路线信息
    private RateInfo rateInfo;             // 费率信息
    
    public ShippingCalculationResult() {}
    
    // 成功结果构造器
    public static ShippingCalculationResult success(String jsonFileName, 
                                                   BigDecimal calculatedDensity,
                                                   Integer matchedDensityFactor,
                                                   BigDecimal baseRate,
                                                   boolean isTransferPoint,
                                                   String basePort,
                                                   String transferPort,
                                                   BigDecimal transferRate,
                                                   BigDecimal baseCost,
                                                   BigDecimal transferCost,
                                                   BigDecimal totalCost,
                                                   String calculationDetails) {
        ShippingCalculationResult result = new ShippingCalculationResult();
        result.jsonFileName = jsonFileName;
        result.calculatedDensity = calculatedDensity;
        result.matchedDensityFactor = matchedDensityFactor;
        result.baseRate = baseRate;
        result.isTransferPoint = isTransferPoint;
        result.basePort = basePort;
        result.transferPort = transferPort;
        result.transferRate = transferRate;
        result.baseCost = baseCost;
        result.transferCost = transferCost;
        result.totalCost = totalCost;
        result.calculationDetails = calculationDetails;
        result.hasError = false;
        return result;
    }
    
    // 错误结果构造器
    public static ShippingCalculationResult error(String jsonFileName, String errorMessage) {
        ShippingCalculationResult result = new ShippingCalculationResult();
        result.jsonFileName = jsonFileName;
        result.errorMessage = errorMessage;
        result.hasError = true;
        return result;
    }
    
    // 内部类：路线信息
    public static class RouteInfo {
        private String origin;
        private String destination;
        private String carrierCode;
        private String frequency;
        
        public RouteInfo(String origin, String destination, String carrierCode, String frequency) {
            this.origin = origin;
            this.destination = destination;
            this.carrierCode = carrierCode;
            this.frequency = frequency;
        }
        
        // Getters and Setters
        public String getOrigin() { return origin; }
        public void setOrigin(String origin) { this.origin = origin; }
        public String getDestination() { return destination; }
        public void setDestination(String destination) { this.destination = destination; }
        public String getCarrierCode() { return carrierCode; }
        public void setCarrierCode(String carrierCode) { this.carrierCode = carrierCode; }
        public String getFrequency() { return frequency; }
        public void setFrequency(String frequency) { this.frequency = frequency; }
    }
    
    // 内部类：费率信息
    public static class RateInfo {
        private Integer minWeight;
        private Integer maxWeight;
        private BigDecimal price;
        private Integer densityFactor;
        
        public RateInfo(Integer minWeight, Integer maxWeight, BigDecimal price, Integer densityFactor) {
            this.minWeight = minWeight;
            this.maxWeight = maxWeight;
            this.price = price;
            this.densityFactor = densityFactor;
        }
        
        // Getters and Setters
        public Integer getMinWeight() { return minWeight; }
        public void setMinWeight(Integer minWeight) { this.minWeight = minWeight; }
        public Integer getMaxWeight() { return maxWeight; }
        public void setMaxWeight(Integer maxWeight) { this.maxWeight = maxWeight; }
        public BigDecimal getPrice() { return price; }
        public void setPrice(BigDecimal price) { this.price = price; }
        public Integer getDensityFactor() { return densityFactor; }
        public void setDensityFactor(Integer densityFactor) { this.densityFactor = densityFactor; }
    }
    
    // Getters and Setters
    public String getJsonFileName() { return jsonFileName; }
    public void setJsonFileName(String jsonFileName) { this.jsonFileName = jsonFileName; }
    
    public BigDecimal getCalculatedDensity() { return calculatedDensity; }
    public void setCalculatedDensity(BigDecimal calculatedDensity) { this.calculatedDensity = calculatedDensity; }
    
    public Integer getMatchedDensityFactor() { return matchedDensityFactor; }
    public void setMatchedDensityFactor(Integer matchedDensityFactor) { this.matchedDensityFactor = matchedDensityFactor; }
    
    public BigDecimal getBaseRate() { return baseRate; }
    public void setBaseRate(BigDecimal baseRate) { this.baseRate = baseRate; }
    
    public boolean isTransferPoint() { return isTransferPoint; }
    public void setTransferPoint(boolean transferPoint) { isTransferPoint = transferPoint; }
    
    public String getBasePort() { return basePort; }
    public void setBasePort(String basePort) { this.basePort = basePort; }
    
    public String getTransferPort() { return transferPort; }
    public void setTransferPort(String transferPort) { this.transferPort = transferPort; }
    
    public BigDecimal getTransferRate() { return transferRate; }
    public void setTransferRate(BigDecimal transferRate) { this.transferRate = transferRate; }
    
    public BigDecimal getBaseCost() { return baseCost; }
    public void setBaseCost(BigDecimal baseCost) { this.baseCost = baseCost; }
    
    public BigDecimal getTransferCost() { return transferCost; }
    public void setTransferCost(BigDecimal transferCost) { this.transferCost = transferCost; }
    
    public BigDecimal getTotalCost() { return totalCost; }
    public void setTotalCost(BigDecimal totalCost) { this.totalCost = totalCost; }
    
    public String getCalculationDetails() { return calculationDetails; }
    public void setCalculationDetails(String calculationDetails) { this.calculationDetails = calculationDetails; }
    
    public boolean isHasError() { return hasError; }
    public void setHasError(boolean hasError) { this.hasError = hasError; }
    
    public String getErrorMessage() { return errorMessage; }
    public void setErrorMessage(String errorMessage) { this.errorMessage = errorMessage; }
    
    public RouteInfo getRouteInfo() { return routeInfo; }
    public void setRouteInfo(RouteInfo routeInfo) { this.routeInfo = routeInfo; }
    
    public RateInfo getRateInfo() { return rateInfo; }
    public void setRateInfo(RateInfo rateInfo) { this.rateInfo = rateInfo; }
}
