package org.example.model;

import java.math.BigDecimal;
import java.util.List;

/**
 * 运价测算请求实体
 */
public class ShippingCalculationRequest {
    
    private String originPort;        // 始发港
    private String destinationPort;   // 目的港
    private BigDecimal weight;        // 重量(kg)
    private BigDecimal volume;        // 体积(cbm)
    private List<String> jsonContents; // JSON报价数据内容列表
    private List<String> fileNames;    // 对应的文件名列表
    
    public ShippingCalculationRequest() {}
    
    public ShippingCalculationRequest(String originPort, String destinationPort,
                                    BigDecimal weight, BigDecimal volume,
                                    List<String> jsonContents, List<String> fileNames) {
        this.originPort = originPort;
        this.destinationPort = destinationPort;
        this.weight = weight;
        this.volume = volume;
        this.jsonContents = jsonContents;
        this.fileNames = fileNames;
    }
    
    public String getOriginPort() {
        return originPort;
    }
    
    public void setOriginPort(String originPort) {
        this.originPort = originPort;
    }
    
    public String getDestinationPort() {
        return destinationPort;
    }
    
    public void setDestinationPort(String destinationPort) {
        this.destinationPort = destinationPort;
    }
    
    public BigDecimal getWeight() {
        return weight;
    }
    
    public void setWeight(BigDecimal weight) {
        this.weight = weight;
    }
    
    public BigDecimal getVolume() {
        return volume;
    }
    
    public void setVolume(BigDecimal volume) {
        this.volume = volume;
    }
    
    public List<String> getJsonContents() {
        return jsonContents;
    }
    
    public void setJsonContents(List<String> jsonContents) {
        this.jsonContents = jsonContents;
    }

    public List<String> getFileNames() {
        return fileNames;
    }

    public void setFileNames(List<String> fileNames) {
        this.fileNames = fileNames;
    }
}
