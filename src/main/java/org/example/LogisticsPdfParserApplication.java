package org.example;

import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.scheduling.annotation.EnableAsync;

import java.io.File;

@SpringBootApplication
@EnableAsync
public class LogisticsPdfParserApplication {

    public static void main(String[] args) {
        // 确保上传目录存在
        File uploadDir = new File("./uploads");
        if (!uploadDir.exists()) {
            uploadDir.mkdirs();
        }
        
        SpringApplication.run(LogisticsPdfParserApplication.class, args);
    }
}
