package org.example.service;

import org.example.model.UploadRecord;
import org.example.repository.UploadRecordRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Service
public class UploadRecordService {

    private final UploadRecordRepository uploadRecordRepository;

    @Autowired
    public UploadRecordService(UploadRecordRepository uploadRecordRepository) {
        this.uploadRecordRepository = uploadRecordRepository;
    }

    public List<UploadRecord> getAllRecords() {
        return uploadRecordRepository.findAllByOrderByUploadTimeDesc();
    }

    public Optional<UploadRecord> getRecord(Long id) {
        return uploadRecordRepository.findById(id);
    }

    public UploadRecord updateRecord(UploadRecord record) {
        return uploadRecordRepository.save(record);
    }
    
    @Transactional
    public void deleteRecord(Long id) {
        uploadRecordRepository.deleteById(id);
    }
}
