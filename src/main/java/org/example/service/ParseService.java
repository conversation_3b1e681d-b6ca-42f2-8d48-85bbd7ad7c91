package org.example.service;

import org.example.model.UploadRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

@Service
public class ParseService {
    
    private final PdfProcessingService pdfProcessingService;
    
    @Autowired
    public ParseService(PdfProcessingService pdfProcessingService) {
        this.pdfProcessingService = pdfProcessingService;
    }
    
    @Async
    public void parseFileAsync(UploadRecord record) {
        // 调用PDF处理服务进行异步处理
        pdfProcessingService.processPdfAsync(record.getId());
    }
}