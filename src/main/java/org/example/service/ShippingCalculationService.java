package org.example.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.example.model.ShippingCalculationRequest;
import org.example.model.ShippingCalculationResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.List;

/**
 * 运价测算服务
 */
@Service
public class ShippingCalculationService {
    
    private static final Logger logger = LoggerFactory.getLogger(ShippingCalculationService.class);
    
    private final ObjectMapper objectMapper;
    
    @Autowired
    public ShippingCalculationService(ObjectMapper objectMapper) {
        this.objectMapper = objectMapper;
    }
    
    /**
     * 执行运价测算
     */
    public List<ShippingCalculationResult> calculateShipping(ShippingCalculationRequest request) {
        List<ShippingCalculationResult> results = new ArrayList<>();
        
        // 计算密度
        BigDecimal density = calculateDensity(request.getWeight(), request.getVolume());
        
        // 对每个JSON文件独立计算
        for (int i = 0; i < request.getJsonContents().size(); i++) {
            String jsonContent = request.getJsonContents().get(i);
            String fileName = "报价文件-" + (i + 1);
            
            try {
                ShippingCalculationResult result = calculateForSingleJson(
                    fileName, jsonContent, request.getOriginPort(), 
                    request.getDestinationPort(), request.getWeight(), density);
                results.add(result);
            } catch (Exception e) {
                logger.error("计算JSON文件 {} 时发生错误: {}", fileName, e.getMessage(), e);
                results.add(ShippingCalculationResult.error(fileName, "计算失败: " + e.getMessage()));
            }
        }
        
        return results;
    }
    
    /**
     * 计算密度
     */
    private BigDecimal calculateDensity(BigDecimal weight, BigDecimal volume) {
        if (volume.compareTo(BigDecimal.ZERO) == 0) {
            throw new IllegalArgumentException("体积不能为0");
        }
        return weight.divide(volume, 2, RoundingMode.HALF_UP);
    }
    
    /**
     * 对单个JSON文件进行计算
     */
    private ShippingCalculationResult calculateForSingleJson(String fileName, String jsonContent, 
                                                           String originPort, String destinationPort, 
                                                           BigDecimal weight, BigDecimal density) throws Exception {
        
        JsonNode rootNode = objectMapper.readTree(jsonContent);
        JsonNode routesNode = rootNode.path("routes");
        
        if (!routesNode.isArray() || routesNode.size() == 0) {
            return ShippingCalculationResult.error(fileName, "未找到路线信息");
        }
        
        // 查找匹配的路线
        logger.debug("开始查找路线匹配 - 始发港: {}, 目的港: {}", originPort, destinationPort);

        for (JsonNode routeNode : routesNode) {
            String routeOrigin = routeNode.path("origin").asText();
            String routeDestination = routeNode.path("destination").asText();

            logger.debug("检查路线 - 路线始发港: {}, 路线目的港: {}", routeOrigin, routeDestination);

            // 检查始发港是否匹配
            if (!originPort.equalsIgnoreCase(routeOrigin)) {
                logger.debug("始发港不匹配，跳过此路线");
                continue;
            }

            logger.debug("始发港匹配成功");

            // 检查目的港是否为转运点
            boolean isTransferPoint = isTransferDestination(routeNode, destinationPort);
            String actualDestination = isTransferPoint ? routeDestination : destinationPort;

            logger.debug("转运点检查结果 - 是否转运点: {}, 实际目的地: {}", isTransferPoint, actualDestination);

            // 如果不是转运点，检查目的港是否直接匹配
            if (!isTransferPoint && !destinationPort.equalsIgnoreCase(routeDestination)) {
                logger.debug("非转运点且目的港不匹配，跳过此路线");
                continue;
            }

            logger.debug("路线匹配成功，开始计算价格");

            // 找到匹配的路线，开始计算
            return calculateRoutePrice(fileName, routeNode, weight, density,
                                     isTransferPoint, destinationPort, actualDestination);
        }
        
        return ShippingCalculationResult.error(fileName, 
            String.format("未找到从 %s 到 %s 的路线", originPort, destinationPort));
    }
    
    /**
     * 检查目的港是否为转运点
     */
    private boolean isTransferDestination(JsonNode routeNode, String destinationPort) {
        JsonNode transfersNode = routeNode.path("transfers");
        if (!transfersNode.isArray()) {
            logger.debug("没有转运信息或转运信息格式错误");
            return false;
        }

        logger.debug("检查转运点 - 目标港口: {}", destinationPort);

        for (JsonNode transferNode : transfersNode) {
            JsonNode destinationsNode = transferNode.path("destinations");
            if (destinationsNode.isArray()) {
                for (JsonNode destNode : destinationsNode) {
                    String toPort = destNode.path("to_port").asText();
                    logger.debug("检查转运目的港: {}", toPort);
                    if (destinationPort.equalsIgnoreCase(toPort)) {
                        logger.debug("找到转运点匹配: {}", toPort);
                        return true;
                    }
                }
            }
        }
        logger.debug("未找到转运点匹配");
        return false;
    }
    
    /**
     * 计算路线价格
     */
    private ShippingCalculationResult calculateRoutePrice(String fileName, JsonNode routeNode, 
                                                        BigDecimal weight, BigDecimal density,
                                                        boolean isTransferPoint, String finalDestination,
                                                        String baseDestination) throws Exception {
        
        // 1. 匹配密度因子
        JsonNode ratesNode = routeNode.path("rates");
        if (!ratesNode.isArray() || ratesNode.size() == 0) {
            return ShippingCalculationResult.error(fileName, "未找到费率信息");
        }
        
        JsonNode matchedRateNode = findMatchingRate(ratesNode, weight, density);
        if (matchedRateNode == null) {
            return ShippingCalculationResult.error(fileName, 
                String.format("未找到匹配的费率 (重量: %s kg, 密度: %s)", weight, density));
        }
        
        Integer matchedDensityFactor = matchedRateNode.path("density_factor").asInt();
        BigDecimal baseRate = new BigDecimal(matchedRateNode.path("price").asText());
        
        // 2. 计算基础运费
        BigDecimal baseCost = baseRate.multiply(weight);
        
        // 3. 处理转运费用
        BigDecimal transferCost = BigDecimal.ZERO;
        BigDecimal transferRate = BigDecimal.ZERO;
        String transferPort = null;
        
        if (isTransferPoint) {
            TransferInfo transferInfo = findTransferInfo(routeNode, finalDestination);
            if (transferInfo != null) {
                transferRate = transferInfo.transferRate;
                transferCost = transferRate.multiply(weight);
                transferPort = finalDestination;
            } else {
                return ShippingCalculationResult.error(fileName, 
                    String.format("未找到到 %s 的转运信息", finalDestination));
            }
        }
        
        // 4. 计算总费用
        BigDecimal totalCost = baseCost.add(transferCost);
        
        // 5. 生成计算详情
        String calculationDetails = buildCalculationDetails(
            matchedRateNode, baseRate, weight, baseCost, 
            isTransferPoint, transferRate, transferCost, totalCost);
        
        // 6. 构建路线和费率信息
        ShippingCalculationResult.RouteInfo routeInfo = new ShippingCalculationResult.RouteInfo(
            routeNode.path("origin").asText(),
            routeNode.path("destination").asText(),
            routeNode.path("carrier_code").asText(),
            routeNode.path("frequency").asText()
        );
        
        ShippingCalculationResult.RateInfo rateInfo = new ShippingCalculationResult.RateInfo(
            matchedRateNode.path("min_wt").asInt(),
            matchedRateNode.path("max_wt").asInt(),
            baseRate,
            matchedDensityFactor
        );
        
        ShippingCalculationResult result = ShippingCalculationResult.success(
            fileName, density, matchedDensityFactor, baseRate,
            isTransferPoint, baseDestination, transferPort, transferRate,
            baseCost, transferCost, totalCost, calculationDetails
        );
        
        result.setRouteInfo(routeInfo);
        result.setRateInfo(rateInfo);
        
        return result;
    }
    
    /**
     * 查找匹配的费率
     * 规则：找到密度因子刚好大于等于计算出的密度的最小的那个密度因子
     */
    private JsonNode findMatchingRate(JsonNode ratesNode, BigDecimal weight, BigDecimal density) {
        JsonNode bestMatch = null;
        Integer minValidDensityFactor = null;

        logger.debug("开始匹配费率 - 重量: {} kg, 密度: {}", weight, density);

        for (JsonNode rateNode : ratesNode) {
            int minWeight = rateNode.path("min_wt").asInt();
            int maxWeight = rateNode.path("max_wt").asInt();
            int densityFactor = rateNode.path("density_factor").asInt();

            logger.debug("检查费率区间 - 重量范围: {}-{} kg, 密度因子: {}", minWeight, maxWeight, densityFactor);

            // 检查重量是否在区间内 (修复：应该是 minWeight <= weight <= maxWeight)
            if (weight.compareTo(new BigDecimal(minWeight)) >= 0 &&
                weight.compareTo(new BigDecimal(maxWeight)) <= 0) {

                logger.debug("重量匹配成功");

                // 检查密度因子是否大于等于计算密度
                if (new BigDecimal(densityFactor).compareTo(density) >= 0) {
                    logger.debug("密度因子匹配成功 - 密度因子: {} >= 计算密度: {}", densityFactor, density);

                    if (minValidDensityFactor == null || densityFactor < minValidDensityFactor) {
                        minValidDensityFactor = densityFactor;
                        bestMatch = rateNode;
                        logger.debug("找到更优匹配 - 密度因子: {}", densityFactor);
                    }
                } else {
                    logger.debug("密度因子不匹配 - 密度因子: {} < 计算密度: {}", densityFactor, density);
                }
            } else {
                logger.debug("重量不匹配 - 重量: {} 不在范围 {}-{} 内", weight, minWeight, maxWeight);
            }
        }

        if (bestMatch != null) {
            logger.debug("最终匹配结果 - 密度因子: {}", minValidDensityFactor);
        } else {
            logger.debug("未找到匹配的费率");
        }

        return bestMatch;
    }
    
    /**
     * 转运信息内部类
     */
    private static class TransferInfo {
        BigDecimal transferRate;
        String fromPort;
        
        TransferInfo(BigDecimal transferRate, String fromPort) {
            this.transferRate = transferRate;
            this.fromPort = fromPort;
        }
    }
    
    /**
     * 查找转运信息
     */
    private TransferInfo findTransferInfo(JsonNode routeNode, String destinationPort) {
        JsonNode transfersNode = routeNode.path("transfers");
        if (!transfersNode.isArray()) {
            return null;
        }
        
        for (JsonNode transferNode : transfersNode) {
            JsonNode destinationsNode = transferNode.path("destinations");
            if (destinationsNode.isArray()) {
                for (JsonNode destNode : destinationsNode) {
                    String toPort = destNode.path("to_port").asText();
                    if (destinationPort.equalsIgnoreCase(toPort)) {
                        BigDecimal transferCost = new BigDecimal(transferNode.path("transfer_cost").asText());
                        String fromPort = transferNode.path("from_port").asText();
                        return new TransferInfo(transferCost, fromPort);
                    }
                }
            }
        }
        return null;
    }
    
    /**
     * 构建计算详情说明
     */
    private String buildCalculationDetails(JsonNode rateNode, BigDecimal baseRate, BigDecimal weight,
                                         BigDecimal baseCost, boolean isTransferPoint, 
                                         BigDecimal transferRate, BigDecimal transferCost, 
                                         BigDecimal totalCost) {
        StringBuilder details = new StringBuilder();
        
        details.append(String.format("重量范围: %d-%d kg; ", 
            rateNode.path("min_wt").asInt(), rateNode.path("max_wt").asInt()));
        details.append(String.format("密度因子: %d; ", rateNode.path("density_factor").asInt()));
        details.append(String.format("基础报价: %s 元/kg; ", baseRate));
        details.append(String.format("基础运费: %s × %s = %s 元; ", baseRate, weight, baseCost));
        
        if (isTransferPoint) {
            details.append(String.format("转运报价: %s 元/kg; ", transferRate));
            details.append(String.format("转运费用: %s × %s = %s 元; ", transferRate, weight, transferCost));
        }
        
        details.append(String.format("总运费: %s 元", totalCost));
        
        return details.toString();
    }
}
