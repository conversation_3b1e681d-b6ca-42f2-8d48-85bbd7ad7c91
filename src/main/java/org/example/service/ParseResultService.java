package org.example.service;

import org.example.model.ParseResult;
import org.example.repository.ParseResultRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ParseResultService {

    private final ParseResultRepository parseResultRepository;

    @Autowired
    public ParseResultService(ParseResultRepository parseResultRepository) {
        this.parseResultRepository = parseResultRepository;
    }

    public Optional<ParseResult> getResultByRecordId(Long recordId) {
        return parseResultRepository.findByRecordId(recordId);
    }

    public ParseResult saveResult(ParseResult result) {
        return parseResultRepository.save(result);
    }

    public List<ParseResult> getAllResults() {
        return parseResultRepository.findAll();
    }

    /**
     * 获取所有关联到未删除上传记录的解析结果
     * 只返回那些能关联到upload_records表中存在记录的解析结果
     */
    public List<ParseResult> getAllValidResults() {
        return parseResultRepository.findAllWithValidUploadRecord();
    }
}