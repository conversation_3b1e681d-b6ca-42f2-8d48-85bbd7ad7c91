package org.example.service;

import org.example.model.ParseResult;
import org.example.repository.ParseResultRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class ParseResultService {

    private final ParseResultRepository parseResultRepository;

    @Autowired
    public ParseResultService(ParseResultRepository parseResultRepository) {
        this.parseResultRepository = parseResultRepository;
    }

    public Optional<ParseResult> getResultByRecordId(Long recordId) {
        return parseResultRepository.findByRecordId(recordId);
    }

    public ParseResult saveResult(ParseResult result) {
        return parseResultRepository.save(result);
    }

    public List<ParseResult> getAllResults() {
        return parseResultRepository.findAll();
    }
}