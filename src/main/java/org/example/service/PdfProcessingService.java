package org.example.service;

import org.apache.pdfbox.pdmodel.PDDocument;
import org.apache.pdfbox.text.PDFTextStripper;
import org.example.model.ParseResult;
import org.example.model.UploadRecord;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;

import java.io.File;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.Date;
import java.util.Optional;

@Service
public class PdfProcessingService {

    private final UploadRecordService uploadRecordService;
    private final FileStorageService fileStorageService;
    private final AiService aiService;
    private final ParseResultService parseResultService;
    
    @Autowired
    public PdfProcessingService(
            UploadRecordService uploadRecordService,
            FileStorageService fileStorageService,
            AiService aiService,
            ParseResultService parseResultService) {
        this.uploadRecordService = uploadRecordService;
        this.fileStorageService = fileStorageService;
        this.aiService = aiService;
        this.parseResultService = parseResultService;
    }
    
    @Async
    public void processPdfAsync(Long recordId) {
        try {
            // 获取记录
            Optional<UploadRecord> recordOpt = uploadRecordService.getRecord(recordId);
            if (!recordOpt.isPresent()) {
                return;
            }
            
            UploadRecord record = recordOpt.get();
            
            // 更新状态为处理中
            record.setStatus("PROCESSING");
            uploadRecordService.updateRecord(record);
            
            // 提取PDF文本
            String pdfText = extractPdfText(record.getFilePath());
            
            // 调用AI解析
            String jsonResult = aiService.parseLogisticsRules(pdfText);
            
            // 保存解析结果
            ParseResult result = new ParseResult();
            result.setRecordId(recordId);
            result.setJsonResult(jsonResult);
            result.setParseTime(new Date());
            parseResultService.saveResult(result);
            
            // 更新状态为完成
            record.setStatus("COMPLETED");
            uploadRecordService.updateRecord(record);
            
        } catch (Exception e) {
            // 处理失败，更新状态
            Optional<UploadRecord> recordOpt = uploadRecordService.getRecord(recordId);
            if (recordOpt.isPresent()) {
                UploadRecord record = recordOpt.get();
                record.setStatus("FAILED");
                record.setErrorMessage(e.getMessage());
                uploadRecordService.updateRecord(record);
            }
        }
    }
    
    private String extractPdfText(String filePath) throws Exception {
        Path fullPath = fileStorageService.getFilePath(filePath);
        StringBuilder text = new StringBuilder();
        try (PDDocument document = PDDocument.load(fullPath.toFile())) {
            PDFTextStripper stripper = new PDFTextStripper();
            text.append(stripper.getText(document));
        }
        return text.toString();
    }
}