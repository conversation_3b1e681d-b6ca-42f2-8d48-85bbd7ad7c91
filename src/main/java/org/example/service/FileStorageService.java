package org.example.service;

import org.example.model.UploadRecord;
import org.example.repository.UploadRecordRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.nio.file.StandardCopyOption;
import java.util.Date;
import java.util.UUID;

@Service
public class FileStorageService {

    private final Path fileStorageLocation;
    private final UploadRecordRepository uploadRecordRepository;

    @Autowired
    public FileStorageService(@Value("${file.upload-dir}") String uploadDir,
                             UploadRecordRepository uploadRecordRepository) {
        this.fileStorageLocation = Paths.get(uploadDir).toAbsolutePath().normalize();
        this.uploadRecordRepository = uploadRecordRepository;
        
        try {
            Files.createDirectories(this.fileStorageLocation);
        } catch (Exception ex) {
            throw new RuntimeException("Could not create the directory where the uploaded files will be stored.", ex);
        }
    }

    public UploadRecord storeFile(MultipartFile file) throws IOException {
        // 规范化文件名
        String originalFilename = StringUtils.cleanPath(file.getOriginalFilename());
        
        // 检查文件名是否包含无效字符
        if (originalFilename.contains("..")) {
            throw new RuntimeException("Filename contains invalid path sequence " + originalFilename);
        }
        
        // 生成唯一文件名
        String uniqueFilename = UUID.randomUUID().toString() + "_" + originalFilename;
        
        // 复制文件到目标位置
        Path targetLocation = this.fileStorageLocation.resolve(uniqueFilename);
        Files.copy(file.getInputStream(), targetLocation, StandardCopyOption.REPLACE_EXISTING);
        
        // 创建上传记录
        UploadRecord record = new UploadRecord();
        record.setFileName(originalFilename);
        record.setFilePath(uniqueFilename);
        record.setFileSize(file.getSize());
        record.setUploadTime(new Date());
        record.setStatus("PENDING");
        
        // 保存记录
        return uploadRecordRepository.save(record);
    }
    
    public Path getFilePath(String fileName) {
        return this.fileStorageLocation.resolve(fileName).normalize();
    }
    
    public void deleteFile(String fileName) throws IOException {
        Path filePath = this.fileStorageLocation.resolve(fileName).normalize();
        Files.deleteIfExists(filePath);
    }
}