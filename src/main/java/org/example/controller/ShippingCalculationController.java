package org.example.controller;

import org.example.model.ShippingCalculationRequest;
import org.example.model.ShippingCalculationResult;
import org.example.service.ShippingCalculationService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 运价测算控制器
 */
@RestController
@RequestMapping("/api/shipping-calculation")
@CrossOrigin
public class ShippingCalculationController {
    
    private static final Logger logger = LoggerFactory.getLogger(ShippingCalculationController.class);
    
    private final ShippingCalculationService calculationService;
    
    @Autowired
    public ShippingCalculationController(ShippingCalculationService calculationService) {
        this.calculationService = calculationService;
    }
    
    /**
     * 执行运价测算
     */
    @PostMapping("/calculate")
    public ResponseEntity<?> calculateShipping(@RequestBody ShippingCalculationRequest request) {
        try {
            // 验证输入参数
            if (request.getOriginPort() == null || request.getOriginPort().trim().isEmpty()) {
                return ResponseEntity.badRequest().body("始发港不能为空");
            }
            
            if (request.getDestinationPort() == null || request.getDestinationPort().trim().isEmpty()) {
                return ResponseEntity.badRequest().body("目的港不能为空");
            }
            
            if (request.getWeight() == null || request.getWeight().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                return ResponseEntity.badRequest().body("重量必须大于0");
            }
            
            if (request.getVolume() == null || request.getVolume().compareTo(java.math.BigDecimal.ZERO) <= 0) {
                return ResponseEntity.badRequest().body("体积必须大于0");
            }
            
            if (request.getJsonContents() == null || request.getJsonContents().isEmpty()) {
                return ResponseEntity.badRequest().body("至少需要上传一个JSON报价文件");
            }
            
            logger.info("开始运价测算: 始发港={}, 目的港={}, 重量={}kg, 体积={}cbm, JSON文件数量={}", 
                       request.getOriginPort(), request.getDestinationPort(), 
                       request.getWeight(), request.getVolume(), request.getJsonContents().size());
            
            // 执行计算
            List<ShippingCalculationResult> results = calculationService.calculateShipping(request);
            
            logger.info("运价测算完成，返回{}个结果", results.size());
            
            return ResponseEntity.ok(results);
            
        } catch (IllegalArgumentException e) {
            logger.warn("输入参数错误: {}", e.getMessage());
            return ResponseEntity.badRequest().body("输入参数错误: " + e.getMessage());
        } catch (Exception e) {
            logger.error("运价测算失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("运价测算失败: " + e.getMessage());
        }
    }
    
    /**
     * 健康检查接口
     */
    @GetMapping("/health")
    public ResponseEntity<String> health() {
        return ResponseEntity.ok("运价测算服务正常运行");
    }
}
