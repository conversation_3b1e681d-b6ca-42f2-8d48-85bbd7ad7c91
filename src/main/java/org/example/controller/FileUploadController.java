package org.example.controller;

import org.example.model.UploadRecord;
import org.example.service.FileStorageService;
import org.example.service.PdfProcessingService;
import org.example.service.UploadRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.HttpStatus;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import java.util.List;

@RestController
@RequestMapping("/api/uploads") // 修改路径，避免与FileController冲突
@CrossOrigin
public class FileUploadController {
    
    private final FileStorageService fileStorageService;
    private final UploadRecordService recordService;
    private final PdfProcessingService pdfProcessingService;
    
    @Autowired
    public FileUploadController(
            FileStorageService fileStorageService,
            UploadRecordService recordService,
            PdfProcessingService pdfProcessingService) {
        this.fileStorageService = fileStorageService;
        this.recordService = recordService;
        this.pdfProcessingService = pdfProcessingService;
    }
    
    @PostMapping("/upload")
    public ResponseEntity<?> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            // 存储文件
            UploadRecord record = fileStorageService.storeFile(file);
            
            // 异步处理PDF
            pdfProcessingService.processPdfAsync(record.getId());
            
            return ResponseEntity.ok(record);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to upload file: " + e.getMessage());
        }
    }
    
    @GetMapping
    public ResponseEntity<List<UploadRecord>> getAllRecords() {
        List<UploadRecord> records = recordService.getAllRecords();
        return ResponseEntity.ok(records);
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<UploadRecord> getRecord(@PathVariable Long id) {
        return recordService.getRecord(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
    
    @DeleteMapping("/{id}")
    public ResponseEntity<Void> deleteRecord(@PathVariable Long id) {
        try {
            recordService.deleteRecord(id);
            return ResponseEntity.ok().build();
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR).build();
        }
    }
}