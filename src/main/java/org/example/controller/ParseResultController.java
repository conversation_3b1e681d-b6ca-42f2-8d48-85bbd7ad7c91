package org.example.controller;

import org.example.model.ParseResult;
import org.example.service.ParseResultService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequestMapping("/api/parse-results")
@CrossOrigin
public class ParseResultController {

    private final ParseResultService parseResultService;

    @Autowired
    public ParseResultController(ParseResultService parseResultService) {
        this.parseResultService = parseResultService;
    }

    @GetMapping("/{recordId}")
    public ResponseEntity<ParseResult> getResultByRecordId(@PathVariable Long recordId) {
        return parseResultService.getResultByRecordId(recordId)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }

    @GetMapping
    public ResponseEntity<List<ParseResult>> getAllParseResults() {
        // 只返回关联到未删除上传记录的解析结果
        List<ParseResult> results = parseResultService.getAllValidResults();
        return ResponseEntity.ok(results);
    }
}