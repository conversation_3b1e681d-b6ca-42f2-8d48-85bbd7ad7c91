
spring.datasource.url=********************************************************************************************************
spring.datasource.driverClassName=com.mysql.cj.jdbc.Driver
spring.datasource.username=root
spring.datasource.password=root
spring.jpa.database-platform=org.hibernate.dialect.MySQL8Dialect
spring.jpa.hibernate.ddl-auto=update
spring.jpa.show-sql=true


spring.servlet.multipart.max-file-size=10MB
spring.servlet.multipart.max-request-size=10MB
file.upload-dir=./uploads


server.port=8080


bailian.api.key=sk-783595093ae84d9ca7d42b664f9ce979
bailian.api.url=https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions


logging.level.org.example=DEBUG
logging.level.org.hibernate.SQL=DEBUG
logging.level.org.hibernate.type.descriptor.sql.BasicBinder=TRACE
