import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.nio.file.Files;
import java.nio.file.Paths;

public class TestCalculation {
    
    public static void main(String[] args) throws Exception {
        // 读取JSON文件
        String jsonContent = Files.readString(Paths.get("json.txt"));
        
        // 测试参数
        String originPort = "PEK";
        String destinationPort = "AMS"; // transfer point
        BigDecimal weight = new BigDecimal("150");
        BigDecimal volume = new BigDecimal("2");
        
        // Calculate density
        BigDecimal density = weight.divide(volume, 2, RoundingMode.HALF_UP);
        System.out.println("Calculated density: " + density);

        // Parse JSON
        ObjectMapper objectMapper = new ObjectMapper();
        JsonNode rootNode = objectMapper.readTree(jsonContent);
        JsonNode routesNode = rootNode.path("routes");

        System.out.println("Start route matching - Origin: " + originPort + ", Destination: " + destinationPort);
        
        for (JsonNode routeNode : routesNode) {
            String routeOrigin = routeNode.path("origin").asText();
            String routeDestination = routeNode.path("destination").asText();
            
            System.out.println("Check route - Route origin: " + routeOrigin + ", Route destination: " + routeDestination);

            // Check if origin port matches
            if (!originPort.equalsIgnoreCase(routeOrigin)) {
                System.out.println("Origin port not match, skip this route");
                continue;
            }

            System.out.println("Origin port matched successfully");

            // Check if destination port is transfer point
            boolean isTransferPoint = isTransferDestination(routeNode, destinationPort);
            String actualDestination = isTransferPoint ? routeDestination : destinationPort;

            System.out.println("Transfer point check result - Is transfer point: " + isTransferPoint + ", Actual destination: " + actualDestination);

            // If not transfer point, check if destination port directly matches
            if (!isTransferPoint && !destinationPort.equalsIgnoreCase(routeDestination)) {
                System.out.println("Not transfer point and destination port not match, skip this route");
                continue;
            }

            System.out.println("Route matched successfully, start finding rates");
            
            // Find matching rate
            JsonNode ratesNode = routeNode.path("rates");
            JsonNode matchedRateNode = findMatchingRate(ratesNode, weight, density);

            if (matchedRateNode == null) {
                System.out.println("No matching rate found");
                continue;
            }

            Integer matchedDensityFactor = matchedRateNode.path("density_factor").asInt();
            BigDecimal baseRate = new BigDecimal(matchedRateNode.path("price").asText());

            System.out.println("Matched density factor: " + matchedDensityFactor);
            System.out.println("Base rate: " + baseRate);

            // Calculate base cost
            BigDecimal baseCost = baseRate.multiply(weight);
            System.out.println("Base cost: " + baseCost);

            // Handle transfer cost
            BigDecimal transferCost = BigDecimal.ZERO;
            BigDecimal transferRate = BigDecimal.ZERO;

            if (isTransferPoint) {
                TransferInfo transferInfo = findTransferInfo(routeNode, destinationPort);
                if (transferInfo != null) {
                    transferRate = transferInfo.transferRate;
                    transferCost = transferRate.multiply(weight);
                    System.out.println("Transfer rate: " + transferRate);
                    System.out.println("Transfer cost: " + transferCost);
                }
            }

            // Calculate total cost
            BigDecimal totalCost = baseCost.add(transferCost);
            System.out.println("Total cost: " + totalCost);

            return; // Exit when match found
        }

        System.out.println("No matching route found");
    }
    
    private static boolean isTransferDestination(JsonNode routeNode, String destinationPort) {
        JsonNode transfersNode = routeNode.path("transfers");
        if (!transfersNode.isArray()) {
            System.out.println("No transfer info or transfer info format error");
            return false;
        }

        System.out.println("Check transfer point - Target port: " + destinationPort);

        for (JsonNode transferNode : transfersNode) {
            JsonNode destinationsNode = transferNode.path("destinations");
            if (destinationsNode.isArray()) {
                for (JsonNode destNode : destinationsNode) {
                    String toPort = destNode.path("to_port").asText();
                    System.out.println("Check transfer destination port: " + toPort);
                    if (destinationPort.equalsIgnoreCase(toPort)) {
                        System.out.println("Found transfer point match: " + toPort);
                        return true;
                    }
                }
            }
        }
        System.out.println("No transfer point match found");
        return false;
    }
    
    private static JsonNode findMatchingRate(JsonNode ratesNode, BigDecimal weight, BigDecimal density) {
        JsonNode bestMatch = null;
        Integer minValidDensityFactor = null;
        
        System.out.println("Start rate matching - Weight: " + weight + " kg, Density: " + density);

        for (JsonNode rateNode : ratesNode) {
            int minWeight = rateNode.path("min_wt").asInt();
            int maxWeight = rateNode.path("max_wt").asInt();
            int densityFactor = rateNode.path("density_factor").asInt();

            System.out.println("Check rate range - Weight range: " + minWeight + "-" + maxWeight + " kg, Density factor: " + densityFactor);

            // Check if weight is in range (Fixed: should be minWeight <= weight <= maxWeight)
            if (weight.compareTo(new BigDecimal(minWeight)) >= 0 &&
                weight.compareTo(new BigDecimal(maxWeight)) <= 0) {

                System.out.println("Weight matched successfully");

                // Check if density factor is greater than or equal to calculated density
                if (new BigDecimal(densityFactor).compareTo(density) >= 0) {
                    System.out.println("Density factor matched successfully - Density factor: " + densityFactor + " >= Calculated density: " + density);

                    if (minValidDensityFactor == null || densityFactor < minValidDensityFactor) {
                        minValidDensityFactor = densityFactor;
                        bestMatch = rateNode;
                        System.out.println("Found better match - Density factor: " + densityFactor);
                    }
                } else {
                    System.out.println("Density factor not matched - Density factor: " + densityFactor + " < Calculated density: " + density);
                }
            } else {
                System.out.println("Weight not matched - Weight: " + weight + " not in range " + minWeight + "-" + maxWeight);
            }
        }

        if (bestMatch != null) {
            System.out.println("Final match result - Density factor: " + minValidDensityFactor);
        } else {
            System.out.println("No matching rate found");
        }
        
        return bestMatch;
    }
    
    private static TransferInfo findTransferInfo(JsonNode routeNode, String destinationPort) {
        JsonNode transfersNode = routeNode.path("transfers");
        if (!transfersNode.isArray()) {
            return null;
        }
        
        for (JsonNode transferNode : transfersNode) {
            JsonNode destinationsNode = transferNode.path("destinations");
            if (destinationsNode.isArray()) {
                for (JsonNode destNode : destinationsNode) {
                    String toPort = destNode.path("to_port").asText();
                    if (destinationPort.equalsIgnoreCase(toPort)) {
                        BigDecimal transferCost = new BigDecimal(transferNode.path("transfer_cost").asText());
                        String fromPort = transferNode.path("from_port").asText();
                        return new TransferInfo(transferCost, fromPort);
                    }
                }
            }
        }
        return null;
    }
    
    static class TransferInfo {
        BigDecimal transferRate;
        String fromPort;
        
        TransferInfo(BigDecimal transferRate, String fromPort) {
            this.transferRate = transferRate;
            this.fromPort = fromPort;
        }
    }
}
