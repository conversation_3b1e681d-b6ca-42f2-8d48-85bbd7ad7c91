{
  "currency": "CNY",
  "quote_date": "2023-10-17",
  "quote_number": "",
  "remarks": [
    {
      "content": "以上价格已包括空运附加费",
      "is_important": true,
      "type": "GENERAL"
    },
    {
      "content": "以上价格只适用于普货",
      "is_important": true,
      "type": "GENERAL"
    },
    {
      "content": "汉莎包板4月22日起变为DAILY",
      "is_important": false,
      "type": "GENERAL"
    }
  ],
  "routes": [
    {
      "M-rate": 0,
      "N-rate": 0,
      "carrier_code": "LH",
      "destination": "FRA",
      "frequency": "DAILY",
      "origin": "PEK",
      "rates": [
        {
          "density_factor": 80,
          "density_type": "Y",
          "max_wt": 300,
          "min_wt": 100,
          "price": 26
        }
      ],
      "surcharges": [
        {
            "surcharges_name":"",
            "surcharges_cost":"",
        }
      ],
      "transfers": [
        {
          "cost_currency": "CNY",
          "destinations": [
            {
              "to_port": "AMS"
            },
            {
              "to_port": "BRU"
            }
          ],
          "from_port": "FRA",
          "transfer_cost": 1
        },
        {
          "cost_currency": "CNY",
          "destinations": [

            {
              "to_port": "ZRH"
            }
          ],
          "from_port": "FRA",
          "transfer_cost": 1.5
        },
        {
          "cost_currency": "CNY",
          "destinations": [

            {
              "to_port": "KTW"
            }
          ],
          "from_port": "FRA",
          "transfer_cost": 2
        },
        {
          "cost_currency": "CNY",
          "destinations": [

            {
              "to_port": "ZAG"
            }
          ],
          "from_port": "FRA",
          "transfer_cost": 2.5
        },
        {
          "cost_currency": "CNY",
          "destinations": [

            {
              "to_port": "SOF"
            }
          ],
          "from_port": "FRA",
          "transfer_cost": 3
        },
        {
          "cost_currency": "CNY",
          "destinations": [
            {
              "to_port": "BEG"
            },
            {
              "to_port": "EDI"
            },
            {
              "to_port": "HEL"
            },
            {
              "to_port": "OPO"
            }
          ],
          "from_port": "FRA",
          "transfer_cost": 3.5
        },
        {
          "cost_currency": "CNY",
          "destinations": [

            {
              "to_port": "RIX"
            }
          ],
          "from_port": "FRA",
          "transfer_cost": 4
        },
        {
          "cost_currency": "CNY",
          "destinations": [
            {
              "to_port": "LIS"
            }
          ],
          "from_port": "FRA",
          "transfer_cost": 4.5
        },
        {
          "cost_currency": "CNY",
          "destinations": [

            {
              "to_port": "TLS"
            }
          ],
          "from_port": "FRA",
          "transfer_cost": 5
        },
        {
          "cost_currency": "CNY",
          "destinations": [
            {
              "to_port": "BHX"
            },
            {
              "to_port": "DUB"
            },
            {
              "to_port": "GLA"
            }
          ],
          "from_port": "FRA",
          "transfer_cost": 5.5
        },
        {
          "cost_currency": "CNY",
          "destinations": [
            {
              "to_port": "EMA"
            },
            {
              "to_port": "ORK"
            },
            {
              "to_port": "SNN"
            }
          ],
          "from_port": "FRA",
          "transfer_cost": 6
        },
        {
          "cost_currency": "CNY",
          "destinations": [
            {
              "to_port": "BFS"
            }
          ],
          "from_port": "FRA",
          "transfer_cost": 6.5
        },
        {
          "cost_currency": "CNY",
          "destinations": [
            {
              "to_port": "SKP"
            }
          ],
          "from_port": "FRA",
          "transfer_cost": 7
        },
        {
          "cost_currency": "CNY",
          "destinations": [
            {
              "to_port": "TLL"
            }
          ],
          "from_port": "FRA",
          "transfer_cost": 7.5
        }
      ]
    }
  ],
  "source_file": "",
  "status": "ACTIVE",
  "valid_from": "2024-05-29",
  "valid_to": "2024-08-27",
  "vendor_code": "BJZK"
}