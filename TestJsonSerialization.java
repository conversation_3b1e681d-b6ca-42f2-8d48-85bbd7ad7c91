import com.fasterxml.jackson.databind.ObjectMapper;
import java.math.BigDecimal;

public class TestJsonSerialization {
    public static void main(String[] args) throws Exception {
        ObjectMapper objectMapper = new ObjectMapper();
        
        // 创建一个测试结果对象
        ShippingCalculationResult result = ShippingCalculationResult.success(
            "test.pdf",                    // fileName
            new BigDecimal("75.00"),       // density
            80,                            // densityFactor
            new BigDecimal("26"),          // baseRate
            true,                          // isTransferPoint
            "FRA",                         // basePort
            "HAJ",                         // transferPort
            new BigDecimal("1"),           // transferRate
            new BigDecimal("5200"),        // baseCost
            new BigDecimal("200"),         // transferCost
            new BigDecimal("5400"),        // totalCost
            "测试计算详情"                  // calculationDetails
        );
        
        // 序列化为JSON
        String json = objectMapper.writeValueAsString(result);
        System.out.println("序列化结果:");
        System.out.println(json);
        
        // 检查关键字段
        System.out.println("\n关键字段检查:");
        System.out.println("isTransferPoint: " + json.contains("\"isTransferPoint\":true"));
        System.out.println("transferRate: " + json.contains("\"transferRate\":1"));
        System.out.println("transferCost: " + json.contains("\"transferCost\":200"));
        
        // 反序列化测试
        ShippingCalculationResult deserialized = objectMapper.readValue(json, ShippingCalculationResult.class);
        System.out.println("\n反序列化结果:");
        System.out.println("isTransferPoint: " + deserialized.isTransferPoint());
        System.out.println("transferRate: " + deserialized.getTransferRate());
        System.out.println("transferCost: " + deserialized.getTransferCost());
    }
}
