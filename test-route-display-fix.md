# 路线显示和费用明细修复

## 修复内容

### 1. 路线显示修复 ✅
**问题**：转运点路线显示错误，显示的是"PEK → FRA"而不是"PEK → HAJ"

**修复**：
- 修改了`ShippingCalculationService.calculateForSingleJson`方法
- 对于转运点，路线信息中的目的地现在显示实际的最终目的地
- 代码变更：
  ```java
  // 对于转运点，显示实际的最终目的地
  String displayDestination = isTransferPoint ? finalDestination : routeNode.path("destination").asText();
  ShippingCalculationResult.RouteInfo routeInfo = new ShippingCalculationResult.RouteInfo(
      routeNode.path("origin").asText(),
      displayDestination,  // 使用实际目的地
      routeNode.path("carrier_code").asText(),
      routeNode.path("frequency").asText()
  );
  ```

### 2. 费用明细显示验证 ✅
**检查**：前端的`getCostBreakdown`方法已经正确实现了转运费用的显示

**逻辑**：
```javascript
getCostBreakdown(result) {
  const breakdown = [
    {
      item: '基础运费',
      calculation: `${result.baseRate} × ${this.form.weight}`,
      amount: result.baseCost
    }
  ];
  
  // 如果是转运点且有转运费用，添加转运费用行
  if (result.isTransferPoint && result.transferCost > 0) {
    breakdown.push({
      item: '转运费用',
      calculation: `${result.transferRate} × ${this.form.weight}`,
      amount: result.transferCost
    });
  }
  
  breakdown.push({
    item: '总计',
    calculation: '-',
    amount: result.totalCost
  });
  
  return breakdown;
}
```

## 预期效果

### 测试用例：PEK → HAJ (转运点)
- **路线显示**：应该显示"PEK → HAJ"而不是"PEK → FRA"
- **费用明细**：应该包含以下项目：
  1. 基础运费：26 × 200 = 5200
  2. 转运费用：1 × 200 = 200
  3. 总计：5400

### 测试用例：PEK → FRA (直达)
- **路线显示**：应该显示"PEK → FRA"
- **费用明细**：应该只包含：
  1. 基础运费：26 × 200 = 5200
  2. 总计：5200

## 修改的文件
- `src/main/java/org/example/service/ShippingCalculationService.java`

## 验证要点
1. 转运点路线应该显示最终目的地
2. 费用明细表格应该单独列出转运费用
3. 计算详情中应该包含转运费用说明
4. 总费用应该正确计算（基础运费 + 转运费用）

## 注意事项
- 前端的`getCostBreakdown`方法已经正确实现，不需要修改
- 后端的转运费率计算逻辑已经正确，只是路线显示需要修复
- 修复后应该重新编译并测试应用程序
