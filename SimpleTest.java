import java.math.BigDecimal;
import java.math.RoundingMode;

public class SimpleTest {
    
    public static void main(String[] args) {
        // Test the weight range matching logic
        System.out.println("=== Testing Weight Range Matching Logic ===");
        
        // Test case 1: Weight 150, Range 100-300
        BigDecimal weight1 = new BigDecimal("150");
        int minWeight1 = 100;
        int maxWeight1 = 300;
        
        boolean match1 = weight1.compareTo(new BigDecimal(minWeight1)) >= 0 && 
                        weight1.compareTo(new BigDecimal(maxWeight1)) <= 0;
        
        System.out.println("Test 1 - Weight: " + weight1 + ", Range: " + minWeight1 + "-" + maxWeight1 + ", Match: " + match1);
        
        // Test case 2: Weight 100, Range 100-300 (boundary test)
        BigDecimal weight2 = new BigDecimal("100");
        boolean match2 = weight2.compareTo(new BigDecimal(minWeight1)) >= 0 && 
                        weight2.compareTo(new BigDecimal(maxWeight1)) <= 0;
        
        System.out.println("Test 2 - Weight: " + weight2 + ", Range: " + minWeight1 + "-" + maxWeight1 + ", Match: " + match2);
        
        // Test case 3: Weight 300, Range 100-300 (boundary test)
        BigDecimal weight3 = new BigDecimal("300");
        boolean match3 = weight3.compareTo(new BigDecimal(minWeight1)) >= 0 && 
                        weight3.compareTo(new BigDecimal(maxWeight1)) <= 0;
        
        System.out.println("Test 3 - Weight: " + weight3 + ", Range: " + minWeight1 + "-" + maxWeight1 + ", Match: " + match3);
        
        // Test case 4: Weight 99, Range 100-300 (should not match)
        BigDecimal weight4 = new BigDecimal("99");
        boolean match4 = weight4.compareTo(new BigDecimal(minWeight1)) >= 0 && 
                        weight4.compareTo(new BigDecimal(maxWeight1)) <= 0;
        
        System.out.println("Test 4 - Weight: " + weight4 + ", Range: " + minWeight1 + "-" + maxWeight1 + ", Match: " + match4);
        
        // Test case 5: Weight 301, Range 100-300 (should not match)
        BigDecimal weight5 = new BigDecimal("301");
        boolean match5 = weight5.compareTo(new BigDecimal(minWeight1)) >= 0 && 
                        weight5.compareTo(new BigDecimal(maxWeight1)) <= 0;
        
        System.out.println("Test 5 - Weight: " + weight5 + ", Range: " + minWeight1 + "-" + maxWeight1 + ", Match: " + match5);
        
        System.out.println("\n=== Testing Density Factor Matching Logic ===");
        
        // Test density factor matching
        // Test case: Weight 150kg, Volume 2cbm, Density = 75
        BigDecimal weight = new BigDecimal("150");
        BigDecimal volume = new BigDecimal("2");
        BigDecimal density = weight.divide(volume, 2, RoundingMode.HALF_UP);
        
        System.out.println("Calculated density: " + density);
        
        // Available density factors: 80 (from JSON sample)
        int densityFactor1 = 80;
        boolean densityMatch1 = new BigDecimal(densityFactor1).compareTo(density) >= 0;
        
        System.out.println("Density factor " + densityFactor1 + " >= " + density + ": " + densityMatch1);
        
        // Test with density factor 70 (should not match)
        int densityFactor2 = 70;
        boolean densityMatch2 = new BigDecimal(densityFactor2).compareTo(density) >= 0;
        
        System.out.println("Density factor " + densityFactor2 + " >= " + density + ": " + densityMatch2);
        
        System.out.println("\n=== Testing Complete Matching Logic ===");
        
        // Complete test based on JSON sample
        // JSON has: min_wt: 100, max_wt: 300, density_factor: 80, price: 26
        // Test input: weight: 150, volume: 2, density: 75
        
        boolean weightInRange = weight.compareTo(new BigDecimal(100)) >= 0 && 
                               weight.compareTo(new BigDecimal(300)) <= 0;
        boolean densityFactorValid = new BigDecimal(80).compareTo(density) >= 0;
        
        System.out.println("Weight " + weight + " in range 100-300: " + weightInRange);
        System.out.println("Density factor 80 >= density " + density + ": " + densityFactorValid);
        System.out.println("Should match: " + (weightInRange && densityFactorValid));
        
        if (weightInRange && densityFactorValid) {
            BigDecimal baseRate = new BigDecimal("26");
            BigDecimal baseCost = baseRate.multiply(weight);
            BigDecimal transferRate = new BigDecimal("1"); // For AMS transfer
            BigDecimal transferCost = transferRate.multiply(weight);
            BigDecimal totalCost = baseCost.add(transferCost);
            
            System.out.println("Base rate: " + baseRate + " CNY/kg");
            System.out.println("Base cost: " + baseRate + " * " + weight + " = " + baseCost + " CNY");
            System.out.println("Transfer rate: " + transferRate + " CNY/kg");
            System.out.println("Transfer cost: " + transferRate + " * " + weight + " = " + transferCost + " CNY");
            System.out.println("Total cost: " + baseCost + " + " + transferCost + " = " + totalCost + " CNY");
        }
        
        System.out.println("\n=== Test Completed ===");
    }
}
