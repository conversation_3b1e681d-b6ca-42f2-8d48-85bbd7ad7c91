<template>
  <div class="shipping-calculation">
    <h2>运价测算工具</h2>
    
    <!-- 输入表单 -->
    <el-card class="input-card" shadow="hover">
      <div slot="header">
        <span>物流信息输入</span>
      </div>
      
      <el-form :model="form" :rules="rules" ref="calculationForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="始发港" prop="originPort">
              <el-input v-model="form.originPort" placeholder="请输入始发港代码，如：PEK"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目的港" prop="destinationPort">
              <el-input v-model="form.destinationPort" placeholder="请输入目的港代码，如：FRA"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="货物重量" prop="weight">
              <el-input v-model="form.weight" placeholder="请输入重量">
                <template slot="append">千克 (kg)</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="货物体积" prop="volume">
              <el-input v-model="form.volume" placeholder="请输入体积">
                <template slot="append">立方米 (cbm)</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    
    <!-- 数据源选择 -->
    <el-card class="data-source-card" shadow="hover">
      <div slot="header">
        <span>报价数据来源</span>
      </div>

      <el-tabs v-model="dataSourceType" @tab-click="handleDataSourceChange">
        <el-tab-pane label="上传JSON文件" name="upload">
          <el-upload
            class="upload-area"
            drag
            multiple
            :auto-upload="false"
            :on-change="handleFileChange"
            :on-remove="handleFileRemove"
            :file-list="fileList"
            accept=".json">
            <i class="el-icon-upload"></i>
            <div class="el-upload__text">拖拽JSON文件到此处，或 <em>点击选择文件</em></div>
            <div class="el-upload__tip" slot="tip">支持选择多个JSON报价文件进行批量测算</div>
          </el-upload>
          <el-button style="margin-top: 10px;" type="text" @click="clearFiles">清空文件</el-button>
        </el-tab-pane>

        <el-tab-pane label="选择已解析数据" name="existing">
          <div v-if="loadingExistingData" class="loading-existing">
            <el-skeleton :rows="3" animated />
          </div>
          <div v-else>
            <el-table
              :data="existingParseResults"
              @selection-change="handleExistingDataSelection"
              style="width: 100%"
              max-height="300">
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="recordId" label="记录ID" width="80"></el-table-column>
              <el-table-column label="文件名" width="200">
                <template slot-scope="scope">
                  {{ getFileNameByRecordId(scope.row.recordId) }}
                </template>
              </el-table-column>
              <el-table-column prop="parseTime" label="解析时间" :formatter="formatDate"></el-table-column>
              <el-table-column label="操作" width="100">
                <template slot-scope="scope">
                  <el-button size="mini" type="text" @click="previewJsonData(scope.row)">预览</el-button>
                </template>
              </el-table-column>
            </el-table>
            <div style="margin-top: 10px;">
              <el-button type="text" @click="refreshExistingData">刷新数据</el-button>
              <span style="margin-left: 20px; color: #909399;">
                已选择 {{ selectedExistingData.length }} 个数据源
              </span>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </el-card>
    
    <!-- 计算按钮 -->
    <div class="calculate-section">
      <el-button 
        type="primary" 
        size="large" 
        :loading="calculating" 
        @click="calculateShipping"
        :disabled="!canCalculate">
        <i class="el-icon-s-data"></i>
        {{ calculating ? '计算中...' : '开始测算' }}
      </el-button>
    </div>
    
    <!-- 结果展示区域 -->
    <div v-if="results.length > 0" class="results-section">
      <h3>测算结果</h3>
      
      <el-row :gutter="20">
        <el-col :span="24" v-for="(result, index) in results" :key="index">
          <el-card class="result-card" :class="{ 'error-card': result.hasError }" shadow="hover">
            <div slot="header">
              <span class="result-title">
                <i :class="result.hasError ? 'el-icon-warning' : 'el-icon-success'"></i>
                {{ result.jsonFileName }}
              </span>
              <el-tag v-if="!result.hasError" type="success" size="small">计算成功</el-tag>
              <el-tag v-else type="danger" size="small">计算失败</el-tag>
            </div>
            
            <!-- 错误信息 -->
            <div v-if="result.hasError" class="error-content">
              <el-alert
                :title="result.errorMessage"
                type="error"
                :closable="false">
              </el-alert>
            </div>
            
            <!-- 成功结果 -->
            <div v-else class="success-content">
              <!-- 基本信息 -->
              <el-descriptions :column="3" border size="small">
                <el-descriptions-item label="计算密度">
                  {{ result.calculatedDensity }} kg/cbm
                </el-descriptions-item>
                <el-descriptions-item label="匹配密度因子">
                  {{ result.matchedDensityFactor }}
                </el-descriptions-item>
                <el-descriptions-item label="基础报价">
                  {{ result.baseRate }} 元/kg
                </el-descriptions-item>
                <el-descriptions-item label="是否转运">
                  <el-tag :type="result.transferPoint ? 'warning' : 'info'" size="mini">
                    {{ result.transferPoint ? '是' : '否' }}
                  </el-tag>
                </el-descriptions-item>
                <el-descriptions-item label="基础港">
                  {{ result.basePort }}
                </el-descriptions-item>
                <el-descriptions-item label="转运港">
                  {{ result.transferPort || '-' }}
                </el-descriptions-item>
              </el-descriptions>
              
              <!-- 费用明细 -->
              <div class="cost-details">
                <h4>费用明细</h4>
                <el-table :data="getCostBreakdown(result)" size="small" border>
                  <el-table-column prop="item" label="费用项目"></el-table-column>
                  <el-table-column prop="calculation" label="计算方式"></el-table-column>
                  <el-table-column prop="amount" label="金额 (元)"></el-table-column>
                </el-table>
                
                <div class="total-cost">
                  <el-tag type="success" size="large">
                    总运费: {{ result.totalCost }} 元
                  </el-tag>
                </div>
              </div>
              
              <!-- 计算详情 -->
              <div class="calculation-details">
                <h4>计算依据</h4>
                <el-alert
                  :title="result.calculationDetails"
                  type="info"
                  :closable="false">
                </el-alert>
              </div>
              
              <!-- 路线信息 -->
              <div v-if="result.routeInfo" class="route-info">
                <h4>路线信息</h4>
                <el-descriptions :column="2" border size="small">
                  <el-descriptions-item label="承运人代码">
                    {{ result.routeInfo.carrierCode }}
                  </el-descriptions-item>
                  <el-descriptions-item label="航班频率">
                    {{ result.routeInfo.frequency }}
                  </el-descriptions-item>
                </el-descriptions>
              </div>
            </div>
          </el-card>
        </el-col>
      </el-row>
    </div>

    <!-- JSON预览对话框 -->
    <el-dialog
      title="JSON数据预览"
      :visible.sync="jsonPreviewVisible"
      width="70%"
      :before-close="closeJsonPreview">
      <div class="json-preview-content">
        <pre>{{ formattedJsonPreview }}</pre>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeJsonPreview">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'ShippingCalculation',
  data() {
    return {
      form: {
        originPort: '',
        destinationPort: '',
        weight: '',
        volume: ''
      },
      rules: {
        originPort: [
          { required: true, message: '请输入始发港', trigger: 'blur' }
        ],
        destinationPort: [
          { required: true, message: '请输入目的港', trigger: 'blur' }
        ],
        weight: [
          { required: true, message: '请输入重量', trigger: 'blur' },
          { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字', trigger: 'blur' }
        ],
        volume: [
          { required: true, message: '请输入体积', trigger: 'blur' },
          { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字', trigger: 'blur' }
        ]
      },
      dataSourceType: 'upload',
      fileList: [],
      existingParseResults: [],
      selectedExistingData: [],
      uploadRecords: [],
      loadingExistingData: false,
      calculating: false,
      results: [],
      jsonPreviewVisible: false,
      jsonPreviewData: ''
    };
  },
  computed: {
    canCalculate() {
      const hasValidForm = this.form.originPort &&
                          this.form.destinationPort &&
                          this.form.weight &&
                          this.form.volume;

      const hasDataSource = (this.dataSourceType === 'upload' && this.fileList.length > 0) ||
                           (this.dataSourceType === 'existing' && this.selectedExistingData.length > 0);

      return hasValidForm && hasDataSource && !this.calculating;
    },

    formattedJsonPreview() {
      try {
        return JSON.stringify(JSON.parse(this.jsonPreviewData), null, 2);
      } catch (e) {
        return this.jsonPreviewData;
      }
    }
  },
  created() {
    this.loadExistingData();
  },
  methods: {
    async loadExistingData() {
      this.loadingExistingData = true;
      try {
        // 加载解析结果
        const parseResultsResponse = await axios.get('/api/parse-results');
        this.existingParseResults = parseResultsResponse.data;

        // 加载上传记录以获取文件名
        const uploadRecordsResponse = await axios.get('/api/uploads');
        this.uploadRecords = uploadRecordsResponse.data;
      } catch (error) {
        console.error('加载已有数据失败:', error);
        this.$message.error('加载已有数据失败');
      } finally {
        this.loadingExistingData = false;
      }
    },

    handleDataSourceChange() {
      // 切换数据源时清空选择
      this.fileList = [];
      this.selectedExistingData = [];
    },

    handleFileChange(file, fileList) {
      this.fileList = fileList;
    },

    handleFileRemove(file, fileList) {
      this.fileList = fileList;
    },

    clearFiles() {
      this.fileList = [];
    },

    handleExistingDataSelection(selection) {
      this.selectedExistingData = selection;
    },

    refreshExistingData() {
      this.loadExistingData();
    },

    getFileNameByRecordId(recordId) {
      const record = this.uploadRecords.find(r => r.id === recordId);
      return record ? record.fileName : `记录-${recordId}`;
    },

    formatDate(row, column, cellValue) {
      return new Date(cellValue).toLocaleString();
    },

    previewJsonData(parseResult) {
      this.jsonPreviewData = parseResult.jsonResult;
      this.jsonPreviewVisible = true;
    },

    closeJsonPreview() {
      this.jsonPreviewVisible = false;
      this.jsonPreviewData = '';
    },
    
    async calculateShipping() {
      // 验证表单
      const valid = await this.$refs.calculationForm.validate().catch(() => false);
      if (!valid) {
        return;
      }

      // 验证数据源
      if (this.dataSourceType === 'upload' && this.fileList.length === 0) {
        this.$message.warning('请至少上传一个JSON文件');
        return;
      }

      if (this.dataSourceType === 'existing' && this.selectedExistingData.length === 0) {
        this.$message.warning('请至少选择一个已解析的数据源');
        return;
      }

      this.calculating = true;
      this.results = [];

      try {
        let jsonContents = [];

        if (this.dataSourceType === 'upload') {
          // 读取上传的文件内容
          jsonContents = await this.readAllFiles();
        } else {
          // 使用已有的解析结果
          jsonContents = this.selectedExistingData.map(item => item.jsonResult);
        }

        // 构建请求数据
        const requestData = {
          originPort: this.form.originPort.trim().toUpperCase(),
          destinationPort: this.form.destinationPort.trim().toUpperCase(),
          weight: parseFloat(this.form.weight),
          volume: parseFloat(this.form.volume),
          jsonContents: jsonContents
        };

        // 发送计算请求
        const response = await axios.post('/api/shipping-calculation/calculate', requestData);
        this.results = response.data;

        this.$message.success('测算完成！');

      } catch (error) {
        console.error('计算失败:', error);
        if (error.response && error.response.data) {
          this.$message.error('计算失败: ' + error.response.data);
        } else {
          this.$message.error('计算失败: ' + error.message);
        }
      } finally {
        this.calculating = false;
      }
    },
    
    async readAllFiles() {
      const promises = this.fileList.map(fileItem => {
        return new Promise((resolve, reject) => {
          const reader = new FileReader();
          reader.onload = (e) => {
            try {
              // 验证JSON格式
              JSON.parse(e.target.result);
              resolve(e.target.result);
            } catch (error) {
              reject(new Error(`文件 ${fileItem.name} 不是有效的JSON格式`));
            }
          };
          reader.onerror = () => reject(new Error(`读取文件 ${fileItem.name} 失败`));
          reader.readAsText(fileItem.raw);
        });
      });
      
      return Promise.all(promises);
    },
    
    getCostBreakdown(result) {
      const breakdown = [
        {
          item: '基础运费',
          calculation: `${result.baseRate} × ${this.form.weight}`,
          amount: result.baseCost
        }
      ];
      
      if (result.transferPoint && result.transferCost > 0) {
        breakdown.push({
          item: '转运费用',
          calculation: `${result.transferRate} × ${this.form.weight}`,
          amount: result.transferCost
        });
      }
      
      breakdown.push({
        item: '总计',
        calculation: '-',
        amount: result.totalCost
      });
      
      return breakdown;
    }
  }
};
</script>

<style scoped>
.shipping-calculation {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.input-card, .data-source-card {
  margin-bottom: 20px;
}

.upload-area {
  width: 100%;
}

.calculate-section {
  text-align: center;
  margin: 30px 0;
}

.results-section {
  margin-top: 30px;
}

.result-card {
  margin-bottom: 20px;
}

.error-card {
  border-left: 4px solid #f56c6c;
}

.result-title {
  font-weight: bold;
  font-size: 16px;
}

.result-title i {
  margin-right: 8px;
}

.success-content {
  padding: 10px 0;
}

.cost-details, .calculation-details, .route-info {
  margin-top: 20px;
}

.cost-details h4, .calculation-details h4, .route-info h4 {
  margin-bottom: 10px;
  color: #303133;
}

.total-cost {
  text-align: right;
  margin-top: 10px;
}

.error-content {
  padding: 10px 0;
}

.loading-existing {
  padding: 20px;
}

/* JSON预览对话框样式 */
.json-preview-content {
  max-height: 500px;
  overflow-y: auto;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
}

.json-preview-content pre {
  margin: 0;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
}
</style>
