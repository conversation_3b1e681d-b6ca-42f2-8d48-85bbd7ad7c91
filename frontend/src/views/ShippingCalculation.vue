<template>
  <div class="shipping-calculation">
    <h2>运价测算工具</h2>
    
    <!-- 输入表单 -->
    <el-card class="input-card" shadow="hover">
      <div slot="header">
        <span>物流信息输入</span>
      </div>
      
      <el-form :model="form" :rules="rules" ref="calculationForm" label-width="120px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="始发港" prop="originPort">
              <el-input v-model="form.originPort" placeholder="请输入始发港代码，如：PEK"></el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="目的港" prop="destinationPort">
              <el-input v-model="form.destinationPort" placeholder="请输入目的港代码，如：FRA"></el-input>
            </el-form-item>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="货物重量" prop="weight">
              <el-input v-model="form.weight" placeholder="请输入重量">
                <template slot="append">千克 (kg)</template>
              </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="货物体积" prop="volume">
              <el-input v-model="form.volume" placeholder="请输入体积">
                <template slot="append">立方米 (cbm)</template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    
    <!-- 数据源选择 -->
    <el-card class="data-source-card" shadow="hover">
      <div slot="header">
        <span>报价数据来源</span>
      </div>

      <div v-if="loadingExistingData" class="loading-existing">
        <el-skeleton :rows="3" animated />
      </div>
      <div v-else>
        <el-table
          :data="existingParseResults"
          @selection-change="handleExistingDataSelection"
          style="width: 100%"
          max-height="300">
          <el-table-column type="selection" width="55"></el-table-column>
          <el-table-column prop="recordId" label="记录ID" width="80"></el-table-column>
          <el-table-column label="文件名" width="200">
            <template slot-scope="scope">
              {{ getFileNameByRecordId(scope.row.recordId) }}
            </template>
          </el-table-column>
          <el-table-column prop="parseTime" label="解析时间" :formatter="formatDate"></el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button size="mini" type="text" @click="previewJsonData(scope.row)">预览</el-button>
            </template>
          </el-table-column>
        </el-table>
        <div style="margin-top: 10px;">
          <el-button type="text" @click="refreshExistingData">刷新数据</el-button>
          <span style="margin-left: 20px; color: #909399;">
            已选择 {{ selectedExistingData.length }} 个数据源
          </span>
        </div>
      </div>
    </el-card>
    
    <!-- 计算按钮 -->
    <div class="calculate-section">
      <el-button 
        type="primary" 
        size="large" 
        :loading="calculating" 
        @click="calculateShipping"
        :disabled="!canCalculate">
        <i class="el-icon-s-data"></i>
        {{ calculating ? '计算中...' : '开始测算' }}
      </el-button>
    </div>
    
    <!-- 结果展示区域 -->
    <div v-if="sortedResults.length > 0" class="results-section">
      <div class="results-header">
        <h3>测算结果</h3>
        <div class="results-summary">
          共找到 {{ sortedResults.length }} 个报价，已按运费从低到高排序
        </div>
      </div>

      <!-- 结果卡片网格 -->
      <div class="results-grid">
        <el-card
          v-for="(result, index) in sortedResults"
          :key="index"
          class="result-card-compact"
          shadow="hover"
          :class="{ 'error-card': result.hasError, 'success-card': !result.hasError }">

          <div v-if="result.hasError" class="error-content-compact">
            <div class="card-header">
              <h4 class="card-title">{{ result.jsonFileName }}</h4>
              <el-tag type="danger" size="small">计算失败</el-tag>
            </div>
            <div class="error-message">
              {{ result.errorMessage }}
            </div>
          </div>

          <div v-else class="success-content-compact">
            <div class="card-header">
              <h4 class="card-title">{{ result.jsonFileName }}</h4>
              <el-tag type="success" size="small">计算成功</el-tag>
            </div>

            <div class="card-summary">
              <div class="total-cost">
                <span class="cost-label">总运费</span>
                <span class="cost-value">¥{{ result.totalCost }}</span>
              </div>

              <div class="route-info">
                <span class="route">{{ result.routeInfo.origin }} → {{ result.routeInfo.destination }}</span>
                <span class="carrier">{{ result.routeInfo.carrierCode }}</span>
              </div>

              <div class="cost-summary">
                <span>基础运费: ¥{{ result.baseCost }}</span>
                <span v-if="result.isTransferPoint">转运费: ¥{{ result.transferCost }}</span>
              </div>
            </div>

            <div class="card-actions">
              <el-button
                type="text"
                size="small"
                @click="toggleDetails(index)"
                icon="el-icon-arrow-down"
                :class="{ 'expanded': result.showDetails }">
                {{ result.showDetails ? '收起详情' : '查看详情' }}
              </el-button>
            </div>

            <!-- 详细信息展开区域 -->
            <el-collapse-transition>
              <div v-show="result.showDetails" class="card-details">
                <el-divider></el-divider>

                <!-- 基本信息 -->
                <div class="detail-section">
                  <h5>基本信息</h5>
                  <el-row :gutter="16">
                    <el-col :span="8">
                      <div class="detail-item">
                        <span class="detail-label">计算密度:</span>
                        <span class="detail-value">{{ result.calculatedDensity }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="detail-item">
                        <span class="detail-label">匹配密度因子:</span>
                        <span class="detail-value">{{ result.matchedDensityFactor }}</span>
                      </div>
                    </el-col>
                    <el-col :span="8">
                      <div class="detail-item">
                        <span class="detail-label">基础费率:</span>
                        <span class="detail-value">{{ result.baseRate }} 元/kg</span>
                      </div>
                    </el-col>
                  </el-row>
                </div>

                <!-- 路线信息 -->
                <div class="detail-section">
                  <h5>路线信息</h5>
                  <el-row :gutter="16">
                    <el-col :span="12">
                      <div class="detail-item">
                        <span class="detail-label">路线:</span>
                        <span class="detail-value">{{ result.routeInfo.origin }} → {{ result.routeInfo.destination }}</span>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="detail-item">
                        <span class="detail-label">承运商:</span>
                        <span class="detail-value">{{ result.routeInfo.carrierCode }}</span>
                      </div>
                    </el-col>
                    <el-col :span="6">
                      <div class="detail-item">
                        <span class="detail-label">频次:</span>
                        <span class="detail-value">{{ result.routeInfo.frequency }}</span>
                      </div>
                    </el-col>
                  </el-row>
                </div>

                <!-- 费用明细 -->
                <div class="detail-section">
                  <h5>费用明细</h5>
                  <el-table :data="getCostBreakdown(result)" size="small" border>
                    <el-table-column prop="item" label="项目" width="120"></el-table-column>
                    <el-table-column prop="calculation" label="计算方式" width="150"></el-table-column>
                    <el-table-column prop="amount" label="金额(元)" align="right">
                      <template slot-scope="scope">
                        <span :class="{'total-amount': scope.row.item === '总计'}">
                          {{ scope.row.amount }}
                        </span>
                      </template>
                    </el-table-column>
                  </el-table>
                </div>

                <!-- 计算详情 -->
                <div class="detail-section">
                  <h5>计算详情</h5>
                  <p class="calculation-details">{{ result.calculationDetails }}</p>
                </div>
              </div>
            </el-collapse-transition>
          </div>
        </el-card>
      </div>
    </div>

    <!-- JSON预览对话框 -->
    <el-dialog
      title="JSON数据预览"
      :visible.sync="jsonPreviewVisible"
      width="70%"
      :before-close="closeJsonPreview">
      <div class="json-preview-content">
        <pre>{{ formattedJsonPreview }}</pre>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="closeJsonPreview">关闭</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  name: 'ShippingCalculation',
  data() {
    return {
      form: {
        originPort: '',
        destinationPort: '',
        weight: '',
        volume: ''
      },
      rules: {
        originPort: [
          { required: true, message: '请输入始发港', trigger: 'blur' }
        ],
        destinationPort: [
          { required: true, message: '请输入目的港', trigger: 'blur' }
        ],
        weight: [
          { required: true, message: '请输入重量', trigger: 'blur' },
          { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字', trigger: 'blur' }
        ],
        volume: [
          { required: true, message: '请输入体积', trigger: 'blur' },
          { pattern: /^\d+(\.\d+)?$/, message: '请输入有效的数字', trigger: 'blur' }
        ]
      },
      dataSourceType: 'existing',
      existingParseResults: [],
      selectedExistingData: [],
      uploadRecords: [],
      loadingExistingData: false,
      calculating: false,
      results: [],
      jsonPreviewVisible: false,
      jsonPreviewData: ''
    };
  },
  computed: {
    canCalculate() {
      const hasValidForm = this.form.originPort &&
                          this.form.destinationPort &&
                          this.form.weight &&
                          this.form.volume;

      const hasDataSource = this.selectedExistingData.length > 0;

      return hasValidForm && hasDataSource && !this.calculating;
    },

    formattedJsonPreview() {
      try {
        return JSON.stringify(JSON.parse(this.jsonPreviewData), null, 2);
      } catch (e) {
        return this.jsonPreviewData;
      }
    },

    // 按运费排序的结果
    sortedResults() {
      return [...this.results].sort((a, b) => {
        // 错误的结果排在后面
        if (a.hasError && !b.hasError) return 1;
        if (!a.hasError && b.hasError) return -1;
        if (a.hasError && b.hasError) return 0;

        // 按总运费从低到高排序
        return parseFloat(a.totalCost) - parseFloat(b.totalCost);
      });
    }
  },
  created() {
    this.loadExistingData();
  },
  methods: {
    async loadExistingData() {
      this.loadingExistingData = true;
      try {
        // 加载解析结果
        const parseResultsResponse = await axios.get('/api/parse-results');
        this.existingParseResults = parseResultsResponse.data;

        // 加载上传记录以获取文件名
        const uploadRecordsResponse = await axios.get('/api/uploads');
        this.uploadRecords = uploadRecordsResponse.data;
      } catch (error) {
        console.error('加载已有数据失败:', error);
        this.$message.error('加载已有数据失败');
      } finally {
        this.loadingExistingData = false;
      }
    },



    handleExistingDataSelection(selection) {
      this.selectedExistingData = selection;
    },

    refreshExistingData() {
      this.loadExistingData();
    },

    getFileNameByRecordId(recordId) {
      const record = this.uploadRecords.find(r => r.id === recordId);
      return record ? record.fileName : `记录-${recordId}`;
    },

    formatDate(row, column, cellValue) {
      return new Date(cellValue).toLocaleString();
    },

    previewJsonData(parseResult) {
      this.jsonPreviewData = parseResult.jsonResult;
      this.jsonPreviewVisible = true;
    },

    closeJsonPreview() {
      this.jsonPreviewVisible = false;
      this.jsonPreviewData = '';
    },

    // 切换详情显示
    toggleDetails(index) {
      this.$set(this.results[index], 'showDetails', !this.results[index].showDetails);
    },
    
    async calculateShipping() {
      // 验证表单
      const valid = await this.$refs.calculationForm.validate().catch(() => false);
      if (!valid) {
        return;
      }

      // 验证数据源
      if (this.selectedExistingData.length === 0) {
        this.$message.warning('请至少选择一个已解析的数据源');
        return;
      }

      this.calculating = true;
      this.results = [];

      try {
        // 使用已有的解析结果，并包含文件名信息
        let jsonContents = this.selectedExistingData.map(item => item.jsonResult);
        let fileNames = this.selectedExistingData.map(item => this.getFileNameByRecordId(item.recordId));

        // 构建请求数据
        const requestData = {
          originPort: this.form.originPort.trim().toUpperCase(),
          destinationPort: this.form.destinationPort.trim().toUpperCase(),
          weight: parseFloat(this.form.weight),
          volume: parseFloat(this.form.volume),
          jsonContents: jsonContents,
          fileNames: fileNames
        };

        // 发送计算请求
        const response = await axios.post('/api/shipping-calculation/calculate', requestData);
        this.results = response.data;

        this.$message.success('测算完成！');

      } catch (error) {
        console.error('计算失败:', error);
        if (error.response && error.response.data) {
          this.$message.error('计算失败: ' + error.response.data);
        } else {
          this.$message.error('计算失败: ' + error.message);
        }
      } finally {
        this.calculating = false;
      }
    },
    
    getCostBreakdown(result) {
      const breakdown = [
        {
          item: '基础运费',
          calculation: `${result.baseRate} × ${this.form.weight}`,
          amount: result.baseCost
        }
      ];
      
      if (result.isTransferPoint && result.transferCost > 0) {
        breakdown.push({
          item: '转运费用',
          calculation: `${result.transferRate} × ${this.form.weight}`,
          amount: result.transferCost
        });
      }
      
      breakdown.push({
        item: '总计',
        calculation: '-',
        amount: result.totalCost
      });
      
      return breakdown;
    }
  }
};
</script>

<style scoped>
.shipping-calculation {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

.input-card, .data-source-card {
  margin-bottom: 20px;
}

.upload-area {
  width: 100%;
}

.calculate-section {
  text-align: center;
  margin: 30px 0;
}

.results-section {
  margin-top: 30px;
}

.results-header {
  margin-bottom: 20px;
}

.results-header h3 {
  margin: 0 0 8px 0;
  color: #303133;
}

.results-summary {
  color: #909399;
  font-size: 14px;
}

.results-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.result-card-compact {
  border-radius: 8px;
  transition: all 0.3s ease;
}

.result-card-compact:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.success-card {
  border-left: 4px solid #67c23a;
}

.error-card {
  border-left: 4px solid #f56c6c;
}

/* 卡片头部 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.card-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

/* 成功卡片内容 */
.success-content-compact {
  padding: 0;
}

.card-summary {
  margin-bottom: 16px;
}

.total-cost {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  padding: 12px;
  background: linear-gradient(135deg, #67c23a, #85ce61);
  border-radius: 6px;
  color: white;
}

.cost-label {
  font-size: 14px;
  font-weight: 500;
}

.cost-value {
  font-size: 20px;
  font-weight: bold;
}

.route-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
  font-size: 14px;
}

.route {
  color: #303133;
  font-weight: 500;
}

.carrier {
  color: #909399;
  background: #f5f7fa;
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 12px;
}

.cost-summary {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: #606266;
}

/* 卡片操作 */
.card-actions {
  text-align: center;
  margin-top: 12px;
}

.card-actions .el-button.expanded {
  transform: rotate(180deg);
}

/* 详情区域 */
.card-details {
  margin-top: 16px;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h5 {
  margin: 0 0 12px 0;
  font-size: 14px;
  font-weight: 600;
  color: #303133;
  border-bottom: 1px solid #ebeef5;
  padding-bottom: 8px;
}

.detail-item {
  margin-bottom: 8px;
}

.detail-label {
  font-size: 12px;
  color: #909399;
  display: block;
  margin-bottom: 4px;
}

.detail-value {
  font-size: 14px;
  color: #303133;
  font-weight: 500;
}

.total-amount {
  font-weight: bold;
  color: #67c23a;
}

/* 错误卡片内容 */
.error-content-compact {
  padding: 0;
}

.error-message {
  margin-top: 12px;
  padding: 12px;
  background: #fef0f0;
  border: 1px solid #fbc4c4;
  border-radius: 4px;
  color: #f56c6c;
  font-size: 14px;
}

.loading-existing {
  padding: 20px;
}

/* JSON预览对话框样式 */
.json-preview-content {
  max-height: 500px;
  overflow-y: auto;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  padding: 15px;
}

.json-preview-content pre {
  margin: 0;
  white-space: pre-wrap;
  font-family: 'Courier New', monospace;
  font-size: 12px;
  line-height: 1.4;
  color: #333;
}
</style>
