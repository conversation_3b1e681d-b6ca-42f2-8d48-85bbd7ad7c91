<template>
  <div class="record-list">
    <h2>物流报价PDF解析记录</h2>
    
    <el-upload
      class="upload-container"
      action="http://localhost:8080/api/upload"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :before-upload="beforeUpload"
      accept="application/pdf"
      drag>
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">拖拽PDF文件到此处，或 <em>点击上传</em></div>
    </el-upload>
    
    <el-table :data="records" style="width: 100%; margin-top: 20px;">
      <el-table-column prop="fileName" label="文件名"></el-table-column>
      <el-table-column prop="fileSize" label="文件大小" :formatter="formatSize"></el-table-column>
      <el-table-column prop="uploadTime" label="上传时间" :formatter="formatDate"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template slot-scope="scope">
          <el-tag :type="getStatusType(scope.row.status)">{{ getStatusText(scope.row.status) }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" width="200">
        <template slot-scope="scope">
          <el-button 
            size="small" 
            type="primary" 
            @click="viewDetails(scope.row.id)"
            :disabled="scope.row.status !== 'COMPLETED'">
            查看详情
          </el-button>
          <el-button 
            size="small" 
            type="danger" 
            @click="deleteRecord(scope.row.id)">
            删除
          </el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <!-- 确认删除对话框 -->
    <el-dialog
      title="确认删除"
      :visible.sync="deleteDialogVisible"
      width="30%">
      <span>确定要删除这条记录吗？此操作不可恢复。</span>
      <span slot="footer" class="dialog-footer">
        <el-button @click="deleteDialogVisible = false">取消</el-button>
        <el-button type="danger" @click="confirmDelete">确定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
/* eslint-disable */
import axios from 'axios';

export default {
  data() {
    return {
      records: [],
      deleteDialogVisible: false,
      recordToDelete: null
    };
  },
  created() {
    this.fetchRecords();
  },
  methods: {
    fetchRecords() {
      axios.get('/api/upload')
        .then(response => {
          this.records = response.data;
        })
        .catch(error => {
          console.error('Error fetching records:', error);
          this.$message.error('获取记录失败');
        });
    },
    handleUploadSuccess() {
      this.$message.success('上传成功');
      this.fetchRecords();
    },
    handleUploadError(error) {
      this.$message.error('上传失败: ' + error.message);
    },
    beforeUpload(file) {
      const isPDF = file.type === 'application/pdf';
      if (!isPDF) {
        this.$message.error('只能上传PDF文件');
        return false;
      }
      return true;
    },
    formatSize(row) {
      const size = row.fileSize;
      if (size < 1024) {
        return size + ' B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB';
      } else {
        return (size / (1024 * 1024)).toFixed(2) + ' MB';
      }
    },
    formatDate(row) {
      return new Date(row.uploadTime).toLocaleString();
    },
    getStatusType(status) {
      const map = {
        'PENDING': 'info',
        'PROCESSING': 'warning',
        'COMPLETED': 'success',
        'FAILED': 'danger'
      };
      return map[status] || 'info';
    },
    getStatusText(status) {
      const map = {
        'PENDING': '等待处理',
        'PROCESSING': '处理中',
        'COMPLETED': '已完成',
        'FAILED': '失败'
      };
      return map[status] || status;
    },
    viewDetails(id) {
      this.$router.push(`/details/${id}`);
    },
    deleteRecord(id) {
      this.recordToDelete = id;
      this.deleteDialogVisible = true;
    },
    confirmDelete() {
      if (!this.recordToDelete) return;
      
      axios.delete(`/api/upload/${this.recordToDelete}`)
        .then(() => {
          this.$message.success('删除成功');
          this.fetchRecords();
          this.deleteDialogVisible = false;
          this.recordToDelete = null;
        })
        .catch(error => {
          console.error('Error deleting record:', error);
          this.$message.error('删除失败');
        });
    }
  }
};
</script>

<style scoped>
.upload-container {
  width: 100%;
  margin: 20px 0;
}
</style>