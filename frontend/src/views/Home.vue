<script>
import axios from 'axios';

export default {
  data() {
    return {
      records: [],
      loading: false,
      uploadDialogVisible: false,
      uploadFile: null,
      uploadLoading: false
    };
  },
  created() {
    this.fetchRecords();
  },
  methods: {
    fetchRecords() {
      this.loading = true;
      // 修改为新的API路径
      axios.get('/api/uploads')
        .then(response => {
          this.records = response.data;
          this.loading = false;
        })
        .catch(error => {
          console.error('Error fetching records:', error);
          this.$message.error('获取记录失败');
          this.loading = false;
        });
    },
    handleUpload() {
      if (!this.uploadFile) {
        this.$message.warning('请选择文件');
        return;
      }
      
      const formData = new FormData();
      formData.append('file', this.uploadFile);
      
      this.uploadLoading = true;
      // 修改为新的API路径
      axios.post('/api/uploads/upload', formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      })
        .then(response => {
          this.$message.success('上传成功');
          this.uploadDialogVisible = false;
          this.uploadFile = null;
          this.fetchRecords();
          this.uploadLoading = false;
        })
        .catch(error => {
          console.error('Error uploading file:', error);
          this.$message.error('上传失败');
          this.uploadLoading = false;
        });
    },
    handleDelete(id) {
      this.$confirm('确定要删除这条记录吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 修改为新的API路径
        axios.delete(`/api/uploads/${id}`)
          .then(() => {
            this.$message.success('删除成功');
            this.fetchRecords();
          })
          .catch(error => {
            console.error('Error deleting record:', error);
            this.$message.error('删除失败');
          });
      }).catch(() => {
        // 取消删除
      });
    },
    // 其他方法保持不变
  }
};
</script>