<template>
  <div id="app">
    <el-container>
      <el-header>
        <div class="header-content">
          <h1>物流报价PDF解析系统</h1>
          <el-menu
            :default-active="activeIndex"
            class="header-menu"
            mode="horizontal"
            @select="handleSelect"
            background-color="#409EFF"
            text-color="#fff"
            active-text-color="#ffd04b">
            <el-menu-item index="/">
              <i class="el-icon-document"></i>
              PDF解析记录
            </el-menu-item>
            <el-menu-item index="/calculation">
              <i class="el-icon-s-data"></i>
              运价测算工具
            </el-menu-item>
          </el-menu>
        </div>
      </el-header>
      <el-main>
        <router-view/>
      </el-main>
      <el-footer>
        <p>&copy; 2023 物流报价PDF解析系统</p>
      </el-footer>
    </el-container>
  </div>
</template>

<style>
#app {
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  color: #2c3e50;
}

.el-header {
  background-color: #409EFF;
  color: white;
  line-height: 20px;
  text-align: center;
}

.el-footer {
  background-color: #f8f9fa;
  color: #6c757d;
  text-align: center;
  line-height: 60px;
}

.el-main {
  padding: 20px;
  min-height: calc(100vh - 120px);
}
</style>