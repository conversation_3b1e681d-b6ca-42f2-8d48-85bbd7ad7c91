import Vue from 'vue'
import VueRouter from 'vue-router'
import RecordList from '../views/RecordList.vue'
import RecordDetail from '../views/RecordDetail.vue'
import ShippingCalculation from '../views/ShippingCalculation.vue'

Vue.use(VueRouter)

const routes = [
  {
    path: '/',
    name: 'home',
    component: RecordList
  },
  {
    path: '/details/:id',
    name: 'details',
    component: RecordDetail
  },
  {
    path: '/calculation',
    name: 'calculation',
    component: ShippingCalculation
  }
]

const router = new VueRouter({
  mode: 'history',
  base: process.env.BASE_URL,
  routes
})

export default router