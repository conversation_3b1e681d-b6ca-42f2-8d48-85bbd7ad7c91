# 物流报价PDF解析项目 - 与大模型的对话

## 初始需求描述

**我**: 我需要开发一个Java项目，实现以下功能：
1. 用户可以上传物流报价规则的PDF文件
2. 系统调用大模型API解析PDF内容，提取关键信息
3. 将解析结果按固定格式转换为JSON
4. 前端界面展示上传记录列表
5. 用户可以查看详情，左侧显示原始PDF，右侧以表格形式展示解析后的数据
请帮我设计项目架构和实现关键代码。

**AI**: 这是一个很好的项目需求。我会帮你设计一个基于Spring Boot的Web应用。让我们分步骤讨论架构和实现方案。

## 项目架构设计

**我**: 首先，请帮我设计项目的整体架构，包括技术栈选择和主要模块划分。

**AI**: 

### 技术栈选择
- 后端: Spring Boot, Java 17
- 数据库: MySQL (存储上传记录和解析结果)
- PDF处理: Apache PDFBox
- AI集成: OpenAI API (或其他大模型API)
- 前端: Vue.js + Element UI
- 构建工具: Maven

### 主要模块划分
1. **文件上传模块**: 处理PDF文件上传和存储
2. **PDF解析模块**: 使用PDFBox提取PDF文本内容
3. **AI解析模块**: 调用大模型API解析文本，提取结构化数据
4. **数据转换模块**: 将AI返回结果转换为标准JSON格式
5. **记录管理模块**: 管理上传记录和解析结果
6. **前端展示模块**: 展示上传记录列表和详情页面

### 数据流程
1. 用户上传PDF → 存储PDF → 提取文本 → 调用AI解析 → 转换为JSON → 存储结果
2. 用户查看记录 → 获取记录列表 → 选择记录 → 加载PDF和JSON数据 → 前端展示

## 数据库设计

**我**: 请设计数据库表结构，需要存储哪些信息？

**AI**:

### 数据库表设计

#### 1. upload_record 表 (上传记录)
```sql
CREATE TABLE upload_record (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    file_name VARCHAR(255) NOT NULL,
    file_path VARCHAR(255) NOT NULL,
    file_size BIGINT NOT NULL,
    upload_time DATETIME NOT NULL,
    status VARCHAR(20) NOT NULL, -- PENDING, PROCESSING, COMPLETED, FAILED
    user_id VARCHAR(50), -- 如果有用户系统
    error_message TEXT
);
```

#### 2. parse_result 表 (解析结果)
```sql
CREATE TABLE parse_result (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    record_id BIGINT NOT NULL,
    json_result LONGTEXT NOT NULL,
    parse_time DATETIME NOT NULL,
    FOREIGN KEY (record_id) REFERENCES upload_record(id)
);
```

## 后端实现

**我**: 请帮我实现PDF上传和存储的控制器代码。

**AI**:

### PDF上传控制器

```java
@RestController
@RequestMapping("/api/upload")
public class FileUploadController {
    
    private final FileStorageService fileStorageService;
    private final UploadRecordService recordService;
    private final PdfProcessingService pdfProcessingService;
    
    @Autowired
    public FileUploadController(FileStorageService fileStorageService, 
                               UploadRecordService recordService,
                               PdfProcessingService pdfProcessingService) {
        this.fileStorageService = fileStorageService;
        this.recordService = recordService;
        this.pdfProcessingService = pdfProcessingService;
    }
    
    @PostMapping
    public ResponseEntity<?> uploadFile(@RequestParam("file") MultipartFile file) {
        try {
            // 验证文件类型
            if (!file.getContentType().equals("application/pdf")) {
                return ResponseEntity.badRequest().body("Only PDF files are allowed");
            }
            
            // 存储文件
            String filePath = fileStorageService.storeFile(file);
            
            // 创建上传记录
            UploadRecord record = new UploadRecord();
            record.setFileName(file.getOriginalFilename());
            record.setFilePath(filePath);
            record.setFileSize(file.getSize());
            record.setUploadTime(LocalDateTime.now());
            record.setStatus("PENDING");
            
            UploadRecord savedRecord = recordService.saveRecord(record);
            
            // 异步处理PDF
            pdfProcessingService.processPdfAsync(savedRecord.getId());
            
            return ResponseEntity.ok(savedRecord);
        } catch (Exception e) {
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("Failed to upload file: " + e.getMessage());
        }
    }
    
    @GetMapping
    public ResponseEntity<List<UploadRecord>> getAllRecords() {
        List<UploadRecord> records = recordService.getAllRecords();
        return ResponseEntity.ok(records);
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<UploadRecord> getRecord(@PathVariable Long id) {
        return recordService.getRecordById(id)
                .map(ResponseEntity::ok)
                .orElse(ResponseEntity.notFound().build());
    }
}
```

**我**: 现在请实现PDF处理和AI解析的服务代码。

**AI**:

### PDF处理服务

```java
@Service
public class PdfProcessingService {

    private final UploadRecordService recordService;
    private final ParseResultService resultService;
    private final AiService aiService;
    
    private final Logger logger = LoggerFactory.getLogger(PdfProcessingService.class);
    
    @Autowired
    public PdfProcessingService(UploadRecordService recordService, 
                               ParseResultService resultService,
                               AiService aiService) {
        this.recordService = recordService;
        this.resultService = resultService;
        this.aiService = aiService;
    }
    
    @Async
    public void processPdfAsync(Long recordId) {
        try {
            // 更新状态为处理中
            UploadRecord record = recordService.getRecordById(recordId)
                .orElseThrow(() -> new RuntimeException("Record not found"));
            
            record.setStatus("PROCESSING");
            recordService.saveRecord(record);
            
            // 提取PDF文本
            String pdfText = extractPdfText(record.getFilePath());
            
            // 调用AI解析
            String jsonResult = aiService.parseLogisticsRules(pdfText);
            
            // 保存解析结果
            ParseResult result = new ParseResult();
            result.setRecordId(recordId);
            result.setJsonResult(jsonResult);
            result.setParseTime(LocalDateTime.now());
            resultService.saveResult(result);
            
            // 更新状态为完成
            record.setStatus("COMPLETED");
            recordService.saveRecord(record);
            
        } catch (Exception e) {
            logger.error("Error processing PDF", e);
            
            // 更新状态为失败
            recordService.getRecordById(recordId).ifPresent(record -> {
                record.setStatus("FAILED");
                record.setErrorMessage(e.getMessage());
                recordService.saveRecord(record);
            });
        }
    }
    
    private String extractPdfText(String filePath) throws IOException {
        StringBuilder text = new StringBuilder();
        try (PDDocument document = PDDocument.load(new File(filePath))) {
            PDFTextStripper stripper = new PDFTextStripper();
            text.append(stripper.getText(document));
        }
        return text.toString();
    }
}
```

### AI服务

```java
@Service
public class AiService {

    @Value("${openai.api.key}")
    private String apiKey;
    
    @Value("${openai.api.url}")
    private String apiUrl;
    
    private final RestTemplate restTemplate;
    private final ObjectMapper objectMapper;
    
    @Autowired
    public AiService(RestTemplate restTemplate, ObjectMapper objectMapper) {
        this.restTemplate = restTemplate;
        this.objectMapper = objectMapper;
    }
    
    public String parseLogisticsRules(String pdfText) throws JsonProcessingException {
        // 构建提示词
        String prompt = buildPrompt(pdfText);
        
        // 构建请求
        Map<String, Object> request = new HashMap<>();
        request.put("model", "gpt-4");
        
        List<Map<String, String>> messages = new ArrayList<>();
        messages.add(Map.of("role", "system", "content", 
            "You are a logistics pricing expert. Extract structured data from logistics pricing documents."));
        messages.add(Map.of("role", "user", "content", prompt));
        request.put("messages", messages);
        
        HttpHeaders headers = new HttpHeaders();
        headers.setContentType(MediaType.APPLICATION_JSON);
        headers.setBearerAuth(apiKey);
        
        HttpEntity<Map<String, Object>> entity = new HttpEntity<>(request, headers);
        
        // 发送请求
        ResponseEntity<String> response = restTemplate.postForEntity(apiUrl, entity, String.class);
        
        // 解析响应
        JsonNode rootNode = objectMapper.readTree(response.getBody());
        String content = rootNode.path("choices").get(0).path("message").path("content").asText();
        
        // 确保返回的是有效JSON
        try {
            objectMapper.readTree(content);
            return content;
        } catch (Exception e) {
            // 如果返回的不是JSON，尝试提取JSON部分
            return extractJsonFromText(content);
        }
    }
    
    private String buildPrompt(String pdfText) {
        return "Below is the text extracted from a logistics pricing PDF document. " +
               "Please analyze it and extract the following information in JSON format:\n\n" +
               "1. Shipping routes\n" +
               "2. Price tiers\n" +
               "3. Base rates\n" +
               "4. Additional fees\n" +
               "5. Special conditions\n\n" +
               "Format the response as a valid JSON object with these categories as keys.\n\n" +
               "Document text:\n" + pdfText;
    }
    
    private String extractJsonFromText(String text) {
        // 尝试从文本中提取JSON部分
        int startIndex = text.indexOf("{");
        int endIndex = text.lastIndexOf("}");
        
        if (startIndex >= 0 && endIndex > startIndex) {
            return text.substring(startIndex, endIndex + 1);
        }
        
        // 如果无法提取，返回简单的错误JSON
        return "{\"error\": \"Could not extract valid JSON from AI response\"}";
    }
}
```

**我**: 请帮我实现前端展示页面的关键代码，特别是详情页面的左右分栏展示。

**AI**:

### 前端实现

#### 上传记录列表页面 (RecordList.vue)

```javascript
<template>
  <div class="record-list">
    <h1>物流报价PDF解析记录</h1>
    
    <el-upload
      class="upload-container"
      action="/api/upload"
      :on-success="handleUploadSuccess"
      :on-error="handleUploadError"
      :before-upload="beforeUpload"
      accept="application/pdf"
      drag>
      <i class="el-icon-upload"></i>
      <div class="el-upload__text">拖拽PDF文件到此处，或 <em>点击上传</em></div>
    </el-upload>
    
    <el-table :data="records" style="width: 100%; margin-top: 20px;">
      <el-table-column prop="fileName" label="文件名"></el-table-column>
      <el-table-column prop="fileSize" label="文件大小" :formatter="formatSize"></el-table-column>
      <el-table-column prop="uploadTime" label="上传时间" :formatter="formatDate"></el-table-column>
      <el-table-column prop="status" label="状态">
        <template #default="scope">
          <el-tag :type="getStatusType(scope.row.status)">{{ scope.row.status }}</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作">
        <template #default="scope">
          <el-button 
            size="small" 
            type="primary" 
            @click="viewDetails(scope.row.id)"
            :disabled="scope.row.status !== 'COMPLETED'">
            查看详情
          </el-button>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  data() {
    return {
      records: []
    };
  },
  created() {
    this.fetchRecords();
  },
  methods: {
    fetchRecords() {
      axios.get('/api/upload')
        .then(response => {
          this.records = response.data;
        })
        .catch(error => {
          console.error('Error fetching records:', error);
          this.$message.error('获取记录失败');
        });
    },
    handleUploadSuccess(response) {
      this.$message.success('上传成功');
      this.fetchRecords();
    },
    handleUploadError(error) {
      this.$message.error('上传失败: ' + error.message);
    },
    beforeUpload(file) {
      const isPDF = file.type === 'application/pdf';
      if (!isPDF) {
        this.$message.error('只能上传PDF文件');
        return false;
      }
      return true;
    },
    formatSize(row) {
      const size = row.fileSize;
      if (size < 1024) {
        return size + ' B';
      } else if (size < 1024 * 1024) {
        return (size / 1024).toFixed(2) + ' KB';
      } else {
        return (size / (1024 * 1024)).toFixed(2) + ' MB';
      }
    },
    formatDate(row) {
      return new Date(row.uploadTime).toLocaleString();
    },
    getStatusType(status) {
      const map = {
        'PENDING': 'info',
        'PROCESSING': 'warning',
        'COMPLETED': 'success',
        'FAILED': 'danger'
      };
      return map[status] || 'info';
    },
    viewDetails(id) {
      this.$router.push(`/details/${id}`);
    }
  }
};
</script>
```

#### 详情页面 (RecordDetail.vue)

```javascript
<template>
  <div class="record-detail">
    <el-page-header @back="goBack" :content="record.fileName"></el-page-header>
    
    <div class="detail-container">
      <div class="pdf-viewer">
        <h3>原始PDF文件</h3>
        <iframe :src="pdfViewerUrl" width="100%" height="800px"></iframe>
      </div>
      
      <div class="parsed-data">
        <h3>解析结果</h3>
        
        <el-tabs v-model="activeTab">
          <el-tab-pane label="运输路线" name="routes">
            <el-table :data="parsedData.shippingRoutes || []">
              <el-table-column v-for="col in getColumns('shippingRoutes')" 
                              :key="col.prop" 
                              :prop="col.prop" 
                              :label="col.label">
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="价格等级" name="tiers">
            <el-table :data="parsedData.priceTiers || []">
              <el-table-column v-for="col in getColumns('priceTiers')" 
                              :key="col.prop" 
                              :prop="col.prop" 
                              :label="col.label">
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="基础费率" name="rates">
            <el-table :data="parsedData.baseRates || []">
              <el-table-column v-for="col in getColumns('baseRates')" 
                              :key="col.prop" 
                              :prop="col.prop" 
                              :label="col.label">
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="附加费用" name="fees">
            <el-table :data="parsedData.additionalFees || []">
              <el-table-column v-for="col in getColumns('additionalFees')" 
                              :key="col.prop" 
                              :prop="col.prop" 
                              :label="col.label">
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="特殊条件" name="conditions">
            <el-table :data="parsedData.specialConditions || []">
              <el-table-column v-for="col in getColumns('specialConditions')" 
                              :key="col.prop" 
                              :prop="col.prop" 
                              :label="col.label">
              </el-table-column>
            </el-table>
          </el-tab-pane>
          
          <el-tab-pane label="原始JSON" name="json">
            <pre>{{ jsonString }}</pre>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script>
import axios from 'axios';

export default {
  data() {
    return {
      record: {},
      parseResult: {},
      parsedData: {},
      activeTab: 'routes',
      jsonString: ''
    };
  },
  computed: {
    pdfViewerUrl() {
      return `/api/files/${this.record.filePath}`;
    }
  },
  created() {
    this.fetchRecordDetails();
  },
  methods: {
    goBack() {
      this.$router.push('/');
    },
    fetchRecordDetails() {
      const id = this.$route.params.id;
      
      // 获取记录信息
      axios.get(`/api/upload/${id}`)
        .then(response => {
          this.record = response.data;
          return axios.get(`/api/parse-results/${id}`);
        })
        .then(response => {
          this.parseResult = response.data;
          try {
            this.parsedData = JSON.parse(this.parseResult.jsonResult);
            this.jsonString = JSON.stringify(this.parsedData, null, 2);
          } catch (e) {
            console.error('Error parsing JSON:', e);
            this.$message.error('解析JSON失败');
            this.jsonString = this.parseResult.jsonResult;
          }
        })
        .catch(error => {
          console.error('Error fetching details:', error);
          this.$message.error('获取详情失败');
        });
    },
    getColumns(dataType) {
      // 动态生成表格列
      if (!this.parsedData[dataType] || this.parsedData[dataType].length === 0) {
        return [];
      }
      
      const firstItem = this.parsedData[dataType][0];
      return Object.keys(firstItem).map(key => {
        return {
          prop: key,
          label: this.formatColumnName(key)
        };
      });
    },
    formatColumnName(key) {
      // 将驼峰命名转换为更友好的显示名称
      return key
        .replace(/([A-Z])/g, ' $1')
        .replace(/^./, str => str.toUpperCase());
    }
  }
};
</script>

<style scoped>
.detail-container {
  display: flex;
  margin-top: 20px;
}

.pdf-viewer, .parsed-data {
  flex: